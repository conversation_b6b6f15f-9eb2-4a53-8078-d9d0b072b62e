# Test info

- Name: Quiz Preview Functionality >> should preview quiz navigation
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:345:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=Question 1')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=Question 1')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:381:53
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Quiz Preview" [level=1]
  - paragraph: Preview how your quiz will appear to users
  - link "Back to Edit":
    - /url: /dashboard/quizzes/6835d60860d5a800e982881a/edit
  - heading "Navigation Preview Test" [level=3]
  - paragraph: Testing navigation in preview mode
  - text: Draft 0 Questions 15 minutes 70% to pass Tags available
  - img
  - heading "Preview Mode" [level=3]
  - paragraph: This is how your quiz will appear to users. Your answers won't be saved, and no scores will be recorded.
  - heading "No Questions Added" [level=3]
  - paragraph: Add some questions to your quiz to see the preview.
  - link "Add Questions":
    - /url: /dashboard/quizzes/6835d60860d5a800e982881a/edit?tab=questions
- alert
```

# Test source

```ts
  281 |
  282 |     // Now preview the quiz
  283 |     const previewBtn = page.locator('button:has-text("Preview"), a:has-text("Preview")');
  284 |     if (await previewBtn.isVisible()) {
  285 |       await previewBtn.click();
  286 |
  287 |       // Should show quiz with question
  288 |       await expect(page.locator('text=What is the most common web vulnerability?')).toBeVisible();
  289 |       await expect(page.locator('text=SQL Injection')).toBeVisible();
  290 |       await expect(page.locator('text=Cross-Site Scripting')).toBeVisible();
  291 |     }
  292 |   });
  293 |
  294 |   test('should preview quiz with multiple question types', async ({ page }) => {
  295 |     // Create quiz with multiple question types
  296 |     await page.goto('/dashboard/quizzes/create');
  297 |     await page.fill('input[id="title"]', 'Multi-Type Question Preview');
  298 |     await page.fill('textarea[id="description"]', 'Quiz with different question types');
  299 |     await page.click('button[type="submit"]');
  300 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  301 |
  302 |     // Add multiple choice question
  303 |     await page.click('button:has-text("Questions")');
  304 |     await page.click('button:has-text("Add Question")');
  305 |
  306 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  307 |     if (await questionTypeSelect.isVisible()) {
  308 |       await questionTypeSelect.selectOption('multiple_choice');
  309 |       await page.fill('textarea[id="questionText"]', 'Which protocol is used for secure web communication?');
  310 |
  311 |       const optionInputs = page.locator('input[placeholder*="option"]');
  312 |       if (await optionInputs.count() >= 2) {
  313 |         await optionInputs.nth(0).fill('HTTP');
  314 |         await optionInputs.nth(1).fill('HTTPS');
  315 |       }
  316 |
  317 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  318 |       await page.waitForTimeout(1000);
  319 |
  320 |       // Add true/false question
  321 |       await page.click('button:has-text("Add Question")');
  322 |       await questionTypeSelect.selectOption('true_false');
  323 |       await page.fill('textarea[id="questionText"]', 'HTTPS encrypts all data transmission.');
  324 |
  325 |       const trueFalseOptions = page.locator('input[value="true"], input[value="false"]');
  326 |       if (await trueFalseOptions.count() > 0) {
  327 |         await trueFalseOptions.first().check();
  328 |       }
  329 |
  330 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  331 |       await page.waitForTimeout(1000);
  332 |     }
  333 |
  334 |     // Preview the quiz
  335 |     const previewBtn = page.locator('button:has-text("Preview")');
  336 |     if (await previewBtn.isVisible()) {
  337 |       await previewBtn.click();
  338 |
  339 |       // Should show both questions
  340 |       await expect(page.locator('text=Which protocol is used for secure web communication?')).toBeVisible();
  341 |       await expect(page.locator('text=HTTPS encrypts all data transmission')).toBeVisible();
  342 |     }
  343 |   });
  344 |
  345 |   test('should preview quiz navigation', async ({ page }) => {
  346 |     // Create quiz with multiple questions for navigation testing
  347 |     await page.goto('/dashboard/quizzes/create');
  348 |     await page.fill('input[id="title"]', 'Navigation Preview Test');
  349 |     await page.fill('textarea[id="description"]', 'Testing navigation in preview mode');
  350 |     await page.click('button[type="submit"]');
  351 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  352 |
  353 |     // Add two questions quickly
  354 |     await page.click('button:has-text("Questions")');
  355 |
  356 |     for (let i = 1; i <= 2; i++) {
  357 |       await page.click('button:has-text("Add Question")');
  358 |
  359 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  360 |       if (await questionTypeSelect.isVisible()) {
  361 |         await questionTypeSelect.selectOption('multiple_choice');
  362 |         await page.fill('textarea[id="questionText"]', `Question ${i}: Test question for navigation`);
  363 |
  364 |         const optionInputs = page.locator('input[placeholder*="option"]');
  365 |         if (await optionInputs.count() >= 2) {
  366 |           await optionInputs.nth(0).fill(`Option A for Q${i}`);
  367 |           await optionInputs.nth(1).fill(`Option B for Q${i}`);
  368 |         }
  369 |
  370 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  371 |         await page.waitForTimeout(1000);
  372 |       }
  373 |     }
  374 |
  375 |     // Preview the quiz
  376 |     const previewBtn = page.locator('button:has-text("Preview")');
  377 |     if (await previewBtn.isVisible()) {
  378 |       await previewBtn.click();
  379 |
  380 |       // Should show first question
> 381 |       await expect(page.locator('text=Question 1')).toBeVisible();
      |                                                     ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  382 |
  383 |       // Test navigation
  384 |       const nextBtn = page.locator('button:has-text("Next"), .next-question');
  385 |       if (await nextBtn.isVisible()) {
  386 |         await nextBtn.click();
  387 |         await expect(page.locator('text=Question 2')).toBeVisible();
  388 |
  389 |         // Test previous navigation
  390 |         const prevBtn = page.locator('button:has-text("Previous"), .prev-question');
  391 |         if (await prevBtn.isVisible()) {
  392 |           await prevBtn.click();
  393 |           await expect(page.locator('text=Question 1')).toBeVisible();
  394 |         }
  395 |       }
  396 |     }
  397 |   });
  398 |
  399 |   test('should preview quiz with time limit', async ({ page }) => {
  400 |     // Create quiz with time limit
  401 |     await page.goto('/dashboard/quizzes/create');
  402 |     await page.fill('input[id="title"]', 'Timed Preview Quiz');
  403 |     await page.fill('textarea[id="description"]', 'Quiz with time limit for preview');
  404 |     await page.fill('input[id="timeLimit"]', '10');
  405 |     await page.click('button[type="submit"]');
  406 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  407 |
  408 |     // Add a question
  409 |     await page.click('button:has-text("Questions")');
  410 |     await page.click('button:has-text("Add Question")');
  411 |
  412 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  413 |     if (await questionTypeSelect.isVisible()) {
  414 |       await questionTypeSelect.selectOption('multiple_choice');
  415 |       await page.fill('textarea[id="questionText"]', 'Timed question for preview');
  416 |
  417 |       const optionInputs = page.locator('input[placeholder*="option"]');
  418 |       if (await optionInputs.count() >= 2) {
  419 |         await optionInputs.nth(0).fill('Option A');
  420 |         await optionInputs.nth(1).fill('Option B');
  421 |       }
  422 |
  423 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  424 |       await page.waitForTimeout(1000);
  425 |     }
  426 |
  427 |     // Preview the quiz
  428 |     const previewBtn = page.locator('button:has-text("Preview")');
  429 |     if (await previewBtn.isVisible()) {
  430 |       await previewBtn.click();
  431 |
  432 |       // Should show timer or time limit information
  433 |       const timerElements = page.locator('.timer, .countdown');
  434 |       const timeText = page.locator('text=10');
  435 |       if (await timerElements.count() > 0 || await timeText.count() > 0) {
  436 |         if (await timerElements.count() > 0) {
  437 |           await expect(timerElements.first()).toBeVisible();
  438 |         } else {
  439 |           await expect(timeText.first()).toBeVisible();
  440 |         }
  441 |       }
  442 |     }
  443 |   });
  444 |
  445 |   test('should exit preview mode', async ({ page }) => {
  446 |     // Create and preview a quiz
  447 |     await page.goto('/dashboard/quizzes/create');
  448 |     await page.fill('input[id="title"]', 'Exit Preview Test');
  449 |     await page.fill('textarea[id="description"]', 'Testing exit from preview mode');
  450 |     await page.click('button[type="submit"]');
  451 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  452 |
  453 |     // Enter preview mode
  454 |     const previewBtn = page.locator('button:has-text("Preview")');
  455 |     if (await previewBtn.isVisible()) {
  456 |       await previewBtn.click();
  457 |
  458 |       // Look for exit/close preview button
  459 |       const exitBtn = page.locator('button:has-text("Exit"), button:has-text("Close"), button:has-text("Back to Edit"), .exit-preview');
  460 |       if (await exitBtn.isVisible()) {
  461 |         await exitBtn.click();
  462 |
  463 |         // Should return to edit mode
  464 |         await expect(page.locator('input[id="title"]')).toBeVisible();
  465 |         await expect(page.locator('input[id="title"]')).toHaveValue('Exit Preview Test');
  466 |       }
  467 |     }
  468 |   });
  469 |
  470 |   test('should preview quiz with different settings', async ({ page }) => {
  471 |     // Create quiz with various settings
  472 |     await page.goto('/dashboard/quizzes/create');
  473 |     await page.fill('input[id="title"]', 'Settings Preview Quiz');
  474 |     await page.fill('textarea[id="description"]', 'Quiz with various settings for preview');
  475 |     await page.fill('input[id="passingScore"]', '80');
  476 |     await page.fill('input[id="timeLimit"]', '30');
  477 |     await page.fill('input[id="tags"]', 'preview, testing, settings');
  478 |     await page.click('button[type="submit"]');
  479 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  480 |
  481 |     // Preview the quiz
```