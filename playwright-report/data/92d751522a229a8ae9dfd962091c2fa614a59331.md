# Test info

- Name: <PERSON>rro<PERSON>ling >> should handle concurrent request errors
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:316:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:322:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  222 |     try {
  223 |       await page.goto('/auth/login');
  224 |       await page.fill('input[id="email"]', '<EMAIL>');
  225 |       await page.fill('input[id="password"]', 'user123');
  226 |       await page.click('button[type="submit"]');
  227 |       
  228 |       // Should timeout and show error
  229 |       await page.waitForSelector('.timeout-error, .loading-error', { timeout: 6000 });
  230 |     } catch (error) {
  231 |       // Timeout is expected behavior
  232 |       expect(error.message).toContain('timeout');
  233 |     }
  234 |   });
  235 |
  236 |   test('should handle JavaScript errors gracefully', async ({ page }) => {
  237 |     // Listen for console errors
  238 |     const consoleErrors: string[] = [];
  239 |     page.on('console', msg => {
  240 |       if (msg.type() === 'error') {
  241 |         consoleErrors.push(msg.text());
  242 |       }
  243 |     });
  244 |     
  245 |     // Listen for page errors
  246 |     const pageErrors: string[] = [];
  247 |     page.on('pageerror', error => {
  248 |       pageErrors.push(error.message);
  249 |     });
  250 |     
  251 |     await page.goto('/security-quizzes');
  252 |     
  253 |     // Inject a JavaScript error
  254 |     await page.evaluate(() => {
  255 |       // This should cause an error but not break the page
  256 |       try {
  257 |         (window as any).nonExistentFunction();
  258 |       } catch (e) {
  259 |         console.error('Test error:', e);
  260 |       }
  261 |     });
  262 |     
  263 |     // Page should still be functional
  264 |     await expect(page.locator('h1')).toBeVisible();
  265 |     
  266 |     // Should have caught the error
  267 |     expect(consoleErrors.length).toBeGreaterThan(0);
  268 |   });
  269 |
  270 |   test('should provide error recovery options', async ({ page }) => {
  271 |     // Simulate network error
  272 |     await page.route('**/api/quizzes', route => route.abort());
  273 |     
  274 |     await page.goto('/security-quizzes');
  275 |     
  276 |     // Look for retry button
  277 |     const retryButton = page.locator('button:has-text("Retry"), button:has-text("Try Again"), .retry-button');
  278 |     if (await retryButton.isVisible()) {
  279 |       // Remove network error simulation
  280 |       await page.unroute('**/api/quizzes');
  281 |       
  282 |       // Click retry
  283 |       await retryButton.click();
  284 |       
  285 |       // Should recover and show content
  286 |       await page.waitForTimeout(2000);
  287 |       const quizCards = page.locator('.quiz-card, .quiz-item');
  288 |       if (await quizCards.count() > 0) {
  289 |         await expect(quizCards.first()).toBeVisible();
  290 |       }
  291 |     }
  292 |   });
  293 |
  294 |   test('should handle malformed data gracefully', async ({ page }) => {
  295 |     // Simulate malformed API response
  296 |     await page.route('**/api/quizzes', route => {
  297 |       route.fulfill({
  298 |         status: 200,
  299 |         contentType: 'application/json',
  300 |         body: '{"invalid": json malformed'
  301 |       });
  302 |     });
  303 |     
  304 |     await page.goto('/security-quizzes');
  305 |     
  306 |     // Should handle malformed data without crashing
  307 |     const errorState = page.locator('.error-state, .data-error, text=error loading data');
  308 |     if (await errorState.count() > 0) {
  309 |       await expect(errorState.first()).toBeVisible();
  310 |     } else {
  311 |       // Should at least show the page structure
  312 |       await expect(page.locator('h1')).toBeVisible();
  313 |     }
  314 |   });
  315 |
  316 |   test('should handle concurrent request errors', async ({ page }) => {
  317 |     // Login first
  318 |     await page.goto('/auth/login');
  319 |     await page.fill('input[id="email"]', '<EMAIL>');
  320 |     await page.fill('input[id="password"]', 'user123');
  321 |     await page.click('button[type="submit"]');
> 322 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  323 |     
  324 |     // Simulate errors for multiple concurrent requests
  325 |     let requestCount = 0;
  326 |     await page.route('**/api/**', route => {
  327 |       requestCount++;
  328 |       if (requestCount % 2 === 0) {
  329 |         route.abort();
  330 |       } else {
  331 |         route.continue();
  332 |       }
  333 |     });
  334 |     
  335 |     // Navigate to page that makes multiple API calls
  336 |     await page.goto('/dashboard/analytics');
  337 |     
  338 |     // Should handle partial failures gracefully
  339 |     await page.waitForTimeout(3000);
  340 |     
  341 |     // Page should still be functional
  342 |     await expect(page.locator('h1')).toBeVisible();
  343 |   });
  344 |
  345 |   test('should show user-friendly error messages', async ({ page }) => {
  346 |     await page.goto('/auth/login');
  347 |     
  348 |     // Test various error scenarios
  349 |     const errorScenarios = [
  350 |       { email: '', password: '', expectedError: 'required' },
  351 |       { email: 'invalid-email', password: 'password', expectedError: 'valid email' },
  352 |       { email: '<EMAIL>', password: '123', expectedError: 'password' }
  353 |     ];
  354 |     
  355 |     for (const scenario of errorScenarios) {
  356 |       await page.fill('input[id="email"]', scenario.email);
  357 |       await page.fill('input[id="password"]', scenario.password);
  358 |       await page.click('button[type="submit"]');
  359 |       
  360 |       // Should show user-friendly error message
  361 |       const errorMessage = page.locator('.error-message, .field-error, .validation-error');
  362 |       if (await errorMessage.count() > 0) {
  363 |         const errorText = await errorMessage.first().textContent();
  364 |         expect(errorText?.toLowerCase()).toContain(scenario.expectedError);
  365 |       }
  366 |       
  367 |       // Clear fields for next test
  368 |       await page.fill('input[id="email"]', '');
  369 |       await page.fill('input[id="password"]', '');
  370 |     }
  371 |   });
  372 | });
  373 |
```