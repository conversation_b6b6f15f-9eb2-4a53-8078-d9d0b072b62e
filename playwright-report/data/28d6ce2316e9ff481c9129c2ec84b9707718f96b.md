# Test info

- Name: Performance Tests >> should handle form submissions efficiently
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/performance.spec.ts:261:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/performance.spec.ts:10:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Performance Tests', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as regular user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'user123');
   9 |     await page.click('button[type="submit"]');
>  10 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   11 |   });
   12 |
   13 |   test('should load dashboard quickly', async ({ page }) => {
   14 |     const startTime = Date.now();
   15 |
   16 |     await page.goto('/dashboard');
   17 |     await page.waitForLoadState('networkidle');
   18 |
   19 |     const loadTime = Date.now() - startTime;
   20 |
   21 |     // Dashboard should load within 3 seconds
   22 |     expect(loadTime).toBeLessThan(3000);
   23 |
   24 |     // Should show main content
   25 |     await expect(page.locator('h1')).toBeVisible();
   26 |   });
   27 |
   28 |   test('should load quiz list efficiently', async ({ page }) => {
   29 |     const startTime = Date.now();
   30 |
   31 |     await page.goto('/security-quizzes');
   32 |     await page.waitForLoadState('networkidle');
   33 |
   34 |     const loadTime = Date.now() - startTime;
   35 |
   36 |     // Quiz list should load within 5 seconds
   37 |     expect(loadTime).toBeLessThan(5000);
   38 |
   39 |     // Should show quiz cards
   40 |     const quizCards = page.locator('.quiz-card, .quiz-item');
   41 |     const cardCount = await quizCards.count();
   42 |     expect(cardCount).toBeGreaterThan(0);
   43 |   });
   44 |
   45 |   test('should handle large quiz efficiently', async ({ page }) => {
   46 |     await page.goto('/security-quizzes');
   47 |
   48 |     // Find and start a quiz
   49 |     const quizCard = page.locator('.quiz-card, .quiz-item').first();
   50 |     await quizCard.click();
   51 |
   52 |     const startTime = Date.now();
   53 |
   54 |     // Start quiz
   55 |     const startBtn = page.locator('button:has-text("Start Quiz"), button:has-text("Begin")');
   56 |     if (await startBtn.isVisible()) {
   57 |       await startBtn.click();
   58 |       await page.waitForLoadState('networkidle');
   59 |
   60 |       const loadTime = Date.now() - startTime;
   61 |
   62 |       // Quiz should start within 3 seconds
   63 |       expect(loadTime).toBeLessThan(3000);
   64 |
   65 |       // Should show first question
   66 |       await expect(page.locator('.question-container, .question')).toBeVisible();
   67 |     }
   68 |   });
   69 |
   70 |   test('should search quickly', async ({ page }) => {
   71 |     await page.goto('/security-quizzes');
   72 |
   73 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
   74 |     if (await searchInput.isVisible()) {
   75 |       const startTime = Date.now();
   76 |
   77 |       await searchInput.fill('security');
   78 |       await searchInput.press('Enter');
   79 |
   80 |       // Wait for search results
   81 |       await page.waitForTimeout(100); // Small delay to start search
   82 |       await page.waitForLoadState('networkidle');
   83 |
   84 |       const searchTime = Date.now() - startTime;
   85 |
   86 |       // Search should complete within 2 seconds
   87 |       expect(searchTime).toBeLessThan(2000);
   88 |
   89 |       // Should show results
   90 |       const results = page.locator('.quiz-card, .quiz-item, .no-results');
   91 |       await expect(results).toHaveCount.greaterThan(0);
   92 |     }
   93 |   });
   94 |
   95 |   test('should handle concurrent users simulation', async ({ browser }) => {
   96 |     // Create multiple browser contexts to simulate concurrent users
   97 |     const contexts = await Promise.all([
   98 |       browser.newContext(),
   99 |       browser.newContext(),
  100 |       browser.newContext()
  101 |     ]);
  102 |
  103 |     const pages = await Promise.all(contexts.map(context => context.newPage()));
  104 |
  105 |     const startTime = Date.now();
  106 |
  107 |     // Simulate multiple users accessing the site simultaneously
  108 |     await Promise.all(pages.map(async (page, index) => {
  109 |       await page.goto('/auth/login');
  110 |       await page.fill('input[id="email"]', `user${index}@quizflow.com`);
```