# Test info

- Name: Quiz Taking Experience >> should save quiz progress
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-taking.spec.ts:168:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-taking.spec.ts:10:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Quiz Taking Experience', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as regular user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'user123');
   9 |     await page.click('button[type="submit"]');
>  10 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   11 |   });
   12 |
   13 |   test('should display available quizzes', async ({ page }) => {
   14 |     await page.goto('/security-quizzes');
   15 |
   16 |     // Should show quiz cards
   17 |     await expect(page.locator('.quiz-card')).toHaveCount.greaterThan(0);
   18 |
   19 |     // Each quiz card should have essential information
   20 |     const firstQuiz = page.locator('.quiz-card').first();
   21 |     await expect(firstQuiz.locator('h3')).toBeVisible(); // Title
   22 |     await expect(firstQuiz.locator('text=questions')).toBeVisible(); // Question count
   23 |     await expect(firstQuiz.locator('text=Beginner|Intermediate|Advanced')).toBeVisible(); // Difficulty
   24 |   });
   25 |
   26 |   test('should start a quiz', async ({ page }) => {
   27 |     await page.goto('/security-quizzes');
   28 |
   29 |     // Click on first quiz
   30 |     await page.click('.quiz-card:first-child');
   31 |
   32 |     // Should show quiz details page
   33 |     await expect(page.locator('h1')).toBeVisible();
   34 |     await expect(page.locator('button:has-text("Start Quiz")')).toBeVisible();
   35 |
   36 |     // Start the quiz
   37 |     await page.click('button:has-text("Start Quiz")');
   38 |
   39 |     // Should navigate to quiz taking interface
   40 |     await page.waitForURL(/\/security-quizzes\/.*\/take/);
   41 |     await expect(page.locator('.question-container')).toBeVisible();
   42 |   });
   43 |
   44 |   test('should answer multiple choice questions', async ({ page }) => {
   45 |     // Navigate to a quiz with multiple choice questions
   46 |     await page.goto('/security-quizzes');
   47 |     await page.click('.quiz-card:first-child');
   48 |     await page.click('button:has-text("Start Quiz")');
   49 |
   50 |     await page.waitForURL(/\/security-quizzes\/.*\/take/);
   51 |
   52 |     // Answer first question
   53 |     await page.click('input[type="radio"]:first-child');
   54 |
   55 |     // Should enable next button
   56 |     await expect(page.locator('button:has-text("Next")')).toBeEnabled();
   57 |
   58 |     // Go to next question
   59 |     await page.click('button:has-text("Next")');
   60 |
   61 |     // Should show next question or results
   62 |     await expect(page.locator('.question-container, .results-container')).toBeVisible();
   63 |   });
   64 |
   65 |   test('should answer true/false questions', async ({ page }) => {
   66 |     // Find a quiz with true/false questions
   67 |     await page.goto('/security-quizzes');
   68 |
   69 |     // Look for a quiz that might have true/false questions
   70 |     await page.click('.quiz-card:first-child');
   71 |     await page.click('button:has-text("Start Quiz")');
   72 |
   73 |     await page.waitForURL(/\/security-quizzes\/.*\/take/);
   74 |
   75 |     // If this is a true/false question
   76 |     const trueFalseOptions = page.locator('input[value="true"], input[value="false"]');
   77 |     if (await trueFalseOptions.count() > 0) {
   78 |       await page.click('input[value="true"]');
   79 |       await expect(page.locator('button:has-text("Next")')).toBeEnabled();
   80 |     }
   81 |   });
   82 |
   83 |   test('should show progress indicator', async ({ page }) => {
   84 |     await page.goto('/security-quizzes');
   85 |     await page.click('.quiz-card:first-child');
   86 |     await page.click('button:has-text("Start Quiz")');
   87 |
   88 |     await page.waitForURL(/\/security-quizzes\/.*\/take/);
   89 |
   90 |     // Should show progress bar or indicator
   91 |     await expect(page.locator('.progress-bar, .question-counter')).toBeVisible();
   92 |
   93 |     // Should show current question number
   94 |     await expect(page.locator('text=Question 1')).toBeVisible();
   95 |   });
   96 |
   97 |   test('should allow navigation between questions', async ({ page }) => {
   98 |     await page.goto('/security-quizzes');
   99 |     await page.click('.quiz-card:first-child');
  100 |     await page.click('button:has-text("Start Quiz")');
  101 |
  102 |     await page.waitForURL(/\/security-quizzes\/.*\/take/);
  103 |
  104 |     // Answer first question
  105 |     await page.click('input[type="radio"]:first-child');
  106 |     await page.click('button:has-text("Next")');
  107 |
  108 |     // Should be able to go back
  109 |     if (await page.locator('button:has-text("Previous")').isVisible()) {
  110 |       await page.click('button:has-text("Previous")');
```