# Test info

- Name: Dashboard >> should display dashboard overview
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:13:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:26:26
```

# Page snapshot

```yaml
- alert
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Dashboard" [level=1]
  - heading "Welcome, QuizFlow Admin!" [level=3]
  - paragraph: <PERSON><PERSON> quizzes and view your progress
  - link "Create New Quiz":
    - /url: /dashboard/quizzes/create
  - link "View All Quizzes":
    - /url: /dashboard/quizzes
  - link "Explore Quizzes":
    - /url: /explore
  - heading "Your Stats" [level=3]
  - paragraph: Your quiz activity and performance
  - paragraph: Created Quizzes
  - paragraph: "1"
  - paragraph: Completed Quizzes
  - paragraph: "0"
  - heading "Your Quizzes" [level=3]
  - paragraph: Recently created and updated quizzes
  - list:
    - listitem:
      - paragraph: Testing quizz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6834fcbceb776c464c06c89d
  - link "View all quizzes":
    - /url: /dashboard/quizzes
  - heading "Recent Activity" [level=3]
  - paragraph: Your recent quiz attempts
  - paragraph: You haven't taken any quizzes yet.
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Dashboard', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as admin user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'admin123');
   9 |     await page.click('button[type="submit"]');
   10 |     await page.waitForURL('/dashboard');
   11 |   });
   12 |
   13 |   test('should display dashboard overview', async ({ page }) => {
   14 |     // Should show main dashboard elements
   15 |     await expect(page.locator('h1')).toContainText('Dashboard');
   16 |
   17 |     // Should show navigation menu (desktop) or mobile menu
   18 |     const desktopNav = page.locator('nav:visible');
   19 |     const mobileNav = page.locator('.mobile-nav, .hamburger');
   20 |     const hasNavigation = await desktopNav.count() > 0 || await mobileNav.count() > 0;
   21 |     expect(hasNavigation).toBe(true);
   22 |
   23 |     // Should show stats cards or overview widgets
   24 |     const dashboardElements = page.locator('.stats-card, .overview-widget, .dashboard-card');
   25 |     const elementCount = await dashboardElements.count();
>  26 |     expect(elementCount).toBeGreaterThan(0);
      |                          ^ Error: expect(received).toBeGreaterThan(expected)
   27 |   });
   28 |
   29 |   test('should navigate to different dashboard sections', async ({ page }) => {
   30 |     // Test navigation to quizzes
   31 |     await page.click('a[href="/dashboard/quizzes"], text=Quizzes');
   32 |     await page.waitForURL('/dashboard/quizzes');
   33 |     await expect(page.locator('h1')).toContainText('Quizzes');
   34 |
   35 |     // Test navigation to analytics
   36 |     await page.click('a[href="/dashboard/analytics"], text=Analytics');
   37 |     await page.waitForURL('/dashboard/analytics');
   38 |     await expect(page.locator('h1')).toContainText('Analytics');
   39 |
   40 |     // Test navigation to admin section (admin only)
   41 |     const adminLink = page.locator('a[href="/dashboard/admin"], text=Admin');
   42 |     if (await adminLink.isVisible()) {
   43 |       await adminLink.click();
   44 |       await page.waitForURL('/dashboard/admin');
   45 |       await expect(page.locator('h1')).toContainText('Admin');
   46 |     }
   47 |   });
   48 |
   49 |   test('should display user profile information', async ({ page }) => {
   50 |     // Look for user profile section
   51 |     const profileSection = page.locator('.user-profile, .profile-info, [data-testid="user-profile"]');
   52 |     if (await profileSection.isVisible()) {
   53 |       await expect(profileSection).toContainText('<EMAIL>');
   54 |     }
   55 |
   56 |     // Check for user menu or dropdown
   57 |     const userMenu = page.locator('.user-menu, .profile-dropdown, button:has-text("admin")');
   58 |     if (await userMenu.isVisible()) {
   59 |       await userMenu.click();
   60 |       await expect(page.locator('text=Logout, text=Sign out')).toBeVisible();
   61 |     }
   62 |   });
   63 |
   64 |   test('should show recent activity', async ({ page }) => {
   65 |     // Look for recent activity section
   66 |     const activitySection = page.locator('.recent-activity, .activity-feed, [data-testid="recent-activity"]');
   67 |     if (await activitySection.isVisible()) {
   68 |       await expect(activitySection).toBeVisible();
   69 |
   70 |       // Should show some activity items
   71 |       const activityItems = page.locator('.activity-item, .activity-entry');
   72 |       const activityCount = await activityItems.count();
   73 |       if (activityCount > 0) {
   74 |         await expect(activityItems.first()).toBeVisible();
   75 |       }
   76 |     }
   77 |   });
   78 |
   79 |   test('should display quiz statistics', async ({ page }) => {
   80 |     // Look for quiz statistics
   81 |     const statsElements = page.locator('.quiz-stats, .statistics, .metrics');
   82 |     if (await statsElements.count() > 0) {
   83 |       await expect(statsElements.first()).toBeVisible();
   84 |
   85 |       // Should show numbers or charts
   86 |       const numbers = page.locator('text=/\\d+/');
   87 |       await expect(numbers.first()).toBeVisible();
   88 |     }
   89 |   });
   90 |
   91 |   test('should handle responsive design', async ({ page }) => {
   92 |     // Test mobile viewport
   93 |     await page.setViewportSize({ width: 375, height: 667 });
   94 |     await page.reload();
   95 |
   96 |     // Should still show main content
   97 |     await expect(page.locator('h1')).toBeVisible();
   98 |
   99 |     // Mobile menu should be accessible
  100 |     const mobileMenu = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"]');
  101 |     if (await mobileMenu.isVisible()) {
  102 |       await mobileMenu.click();
  103 |       await expect(page.locator('nav, .navigation')).toBeVisible();
  104 |     }
  105 |
  106 |     // Reset to desktop
  107 |     await page.setViewportSize({ width: 1280, height: 720 });
  108 |   });
  109 |
  110 |   test('should search functionality', async ({ page }) => {
  111 |     // Look for search input
  112 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
  113 |     if (await searchInput.isVisible()) {
  114 |       await searchInput.fill('security');
  115 |       await searchInput.press('Enter');
  116 |
  117 |       // Should show search results or filter content
  118 |       await page.waitForTimeout(1000); // Wait for search to process
  119 |
  120 |       // Verify search worked (results or filtered content)
  121 |       const results = page.locator('.search-results, .filtered-content');
  122 |       if (await results.isVisible()) {
  123 |         await expect(results).toBeVisible();
  124 |       }
  125 |     }
  126 |   });
```