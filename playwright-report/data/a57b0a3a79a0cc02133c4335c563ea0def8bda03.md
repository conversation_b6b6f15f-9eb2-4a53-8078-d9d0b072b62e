# Test info

- Name: Analytics Dashboard >> should display analytics overview
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:16:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/analytics.spec.ts:23:25
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
  - link "Sign In":
    - /url: /auth/login
- main:
  - heading "Analytics Dashboard" [level=1]
  - paragraph: Total Quizzes Created
  - paragraph: "110"
  - paragraph: Quizzes Taken
  - paragraph: "0"
  - paragraph: Responses Received
  - paragraph: "0"
  - paragraph: Average Score
  - paragraph: 0.0%
  - heading "Quiz Performance" [level=3]
  - paragraph: Average scores and response counts for your top quizzes
  - paragraph: No data available
  - heading "Top Quizzes" [level=3]
  - paragraph: Your most popular quizzes by number of responses
  - text: Testing quizz 0 Basic Security Quiz 0 Preview Test Quiz 0 Updated Quiz Title 0 Advanced Penetration Testing Quiz 0
  - heading "Recent Activity" [level=3]
  - paragraph: Recent responses to your quizzes
  - paragraph: No recent activity
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Analytics Dashboard', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as admin user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'admin123');
   9 |     await page.click('button[type="submit"]');
   10 |     await page.waitForURL('/dashboard');
   11 |
   12 |     // Navigate to analytics
   13 |     await page.goto('/dashboard/analytics');
   14 |   });
   15 |
   16 |   test('should display analytics overview', async ({ page }) => {
   17 |     // Should show analytics page title
   18 |     await expect(page.locator('h1')).toContainText('Analytics');
   19 |
   20 |     // Should show key metrics
   21 |     const metrics = page.locator('.metric, .stat-card, .analytics-card');
   22 |     const metricCount = await metrics.count();
>  23 |     expect(metricCount).toBeGreaterThan(0);
      |                         ^ Error: expect(received).toBeGreaterThan(expected)
   24 |
   25 |     // Should show numbers
   26 |     const numbers = page.locator('text=/\\d+/');
   27 |     await expect(numbers.first()).toBeVisible();
   28 |   });
   29 |
   30 |   test('should display quiz performance metrics', async ({ page }) => {
   31 |     // Look for quiz performance section
   32 |     const performanceSection = page.locator('.quiz-performance, .performance-metrics');
   33 |     if (await performanceSection.isVisible()) {
   34 |       await expect(performanceSection).toBeVisible();
   35 |
   36 |       // Should show completion rates
   37 |       const completionRate = page.locator('text=/completion/i, text=/complete/i');
   38 |       if (await completionRate.count() > 0) {
   39 |         await expect(completionRate.first()).toBeVisible();
   40 |       }
   41 |
   42 |       // Should show average scores
   43 |       const averageScore = page.locator('text=/average/i, text=/score/i');
   44 |       if (await averageScore.count() > 0) {
   45 |         await expect(averageScore.first()).toBeVisible();
   46 |       }
   47 |     }
   48 |   });
   49 |
   50 |   test('should display user engagement metrics', async ({ page }) => {
   51 |     // Look for user engagement data
   52 |     const engagementSection = page.locator('.user-engagement, .engagement-metrics');
   53 |     if (await engagementSection.isVisible()) {
   54 |       await expect(engagementSection).toBeVisible();
   55 |
   56 |       // Should show active users
   57 |       const activeUsers = page.locator('text=/active users/i, text=/users/i');
   58 |       if (await activeUsers.count() > 0) {
   59 |         await expect(activeUsers.first()).toBeVisible();
   60 |       }
   61 |     }
   62 |   });
   63 |
   64 |   test('should display charts and graphs', async ({ page }) => {
   65 |     // Look for chart containers
   66 |     const charts = page.locator('.chart, .graph, canvas, svg');
   67 |     if (await charts.count() > 0) {
   68 |       await expect(charts.first()).toBeVisible();
   69 |
   70 |       // Wait for charts to load
   71 |       await page.waitForTimeout(2000);
   72 |
   73 |       // Verify chart has content
   74 |       const chartContent = await charts.first().isVisible();
   75 |       expect(chartContent).toBe(true);
   76 |     }
   77 |   });
   78 |
   79 |   test('should filter analytics by date range', async ({ page }) => {
   80 |     // Look for date range picker
   81 |     const dateFilter = page.locator('.date-picker, .date-range, input[type="date"]');
   82 |     if (await dateFilter.count() > 0) {
   83 |       const firstDateInput = dateFilter.first();
   84 |       await firstDateInput.click();
   85 |
   86 |       // Set a date range
   87 |       await firstDateInput.fill('2024-01-01');
   88 |
   89 |       // Look for apply button or second date input
   90 |       const applyBtn = page.locator('button:has-text("Apply"), button:has-text("Filter")');
   91 |       if (await applyBtn.isVisible()) {
   92 |         await applyBtn.click();
   93 |
   94 |         // Wait for data to update
   95 |         await page.waitForTimeout(1000);
   96 |
   97 |         // Verify data updated (this is basic - real test would check specific values)
   98 |         await expect(page.locator('.metric, .stat-card')).toBeVisible();
   99 |       }
  100 |     }
  101 |   });
  102 |
  103 |   test('should export analytics data', async ({ page }) => {
  104 |     // Look for export button
  105 |     const exportBtn = page.locator('button:has-text("Export"), button:has-text("Download")');
  106 |     if (await exportBtn.isVisible()) {
  107 |       // Set up download handler
  108 |       const downloadPromise = page.waitForEvent('download');
  109 |
  110 |       await exportBtn.click();
  111 |
  112 |       // Wait for download to start
  113 |       const download = await downloadPromise;
  114 |
  115 |       // Verify download started
  116 |       expect(download.suggestedFilename()).toMatch(/\.(csv|xlsx|pdf)$/);
  117 |     }
  118 |   });
  119 |
  120 |   test('should display quiz-specific analytics', async ({ page }) => {
  121 |     // Look for quiz selection dropdown
  122 |     const quizSelector = page.locator('select[name="quiz"], .quiz-selector');
  123 |     if (await quizSelector.isVisible()) {
```