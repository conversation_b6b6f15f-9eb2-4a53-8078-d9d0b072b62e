# Test info

- Name: Accessibility Tests >> should have proper heading hierarchy
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:13:7

# Error details

```
Error: page.waitForURL: Test ended.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/accessibility.spec.ts:10:16
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Accessibility Tests', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as regular user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'user123');
   9 |     await page.click('button[type="submit"]');
>  10 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test ended.
   11 |   });
   12 |
   13 |   test('should have proper heading hierarchy', async ({ page }) => {
   14 |     await page.goto('/security-quizzes');
   15 |     
   16 |     // Check for h1 element
   17 |     const h1Elements = page.locator('h1');
   18 |     await expect(h1Elements).toHaveCount(1);
   19 |     
   20 |     // Check heading hierarchy (h1 -> h2 -> h3, etc.)
   21 |     const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
   22 |     expect(headings.length).toBeGreaterThan(0);
   23 |     
   24 |     // First heading should be h1
   25 |     const firstHeading = page.locator('h1, h2, h3, h4, h5, h6').first();
   26 |     const tagName = await firstHeading.evaluate(el => el.tagName.toLowerCase());
   27 |     expect(tagName).toBe('h1');
   28 |   });
   29 |
   30 |   test('should have proper form labels', async ({ page }) => {
   31 |     // Test login form
   32 |     await page.goto('/auth/login');
   33 |     
   34 |     // All form inputs should have labels
   35 |     const inputs = page.locator('input[type="email"], input[type="password"], input[type="text"]');
   36 |     const inputCount = await inputs.count();
   37 |     
   38 |     for (let i = 0; i < inputCount; i++) {
   39 |       const input = inputs.nth(i);
   40 |       const inputId = await input.getAttribute('id');
   41 |       
   42 |       if (inputId) {
   43 |         // Should have corresponding label
   44 |         const label = page.locator(`label[for="${inputId}"]`);
   45 |         await expect(label).toBeVisible();
   46 |       }
   47 |     }
   48 |   });
   49 |
   50 |   test('should have proper button accessibility', async ({ page }) => {
   51 |     await page.goto('/security-quizzes');
   52 |     
   53 |     // All buttons should have accessible text
   54 |     const buttons = page.locator('button');
   55 |     const buttonCount = await buttons.count();
   56 |     
   57 |     for (let i = 0; i < Math.min(buttonCount, 10); i++) {
   58 |       const button = buttons.nth(i);
   59 |       const text = await button.textContent();
   60 |       const ariaLabel = await button.getAttribute('aria-label');
   61 |       const title = await button.getAttribute('title');
   62 |       
   63 |       // Button should have text, aria-label, or title
   64 |       expect(text || ariaLabel || title).toBeTruthy();
   65 |     }
   66 |   });
   67 |
   68 |   test('should have proper link accessibility', async ({ page }) => {
   69 |     await page.goto('/security-quizzes');
   70 |     
   71 |     // All links should have accessible text
   72 |     const links = page.locator('a');
   73 |     const linkCount = await links.count();
   74 |     
   75 |     for (let i = 0; i < Math.min(linkCount, 10); i++) {
   76 |       const link = links.nth(i);
   77 |       const text = await link.textContent();
   78 |       const ariaLabel = await link.getAttribute('aria-label');
   79 |       const title = await link.getAttribute('title');
   80 |       
   81 |       // Link should have text, aria-label, or title
   82 |       expect(text?.trim() || ariaLabel || title).toBeTruthy();
   83 |     }
   84 |   });
   85 |
   86 |   test('should have proper image alt text', async ({ page }) => {
   87 |     await page.goto('/security-quizzes');
   88 |     
   89 |     // All images should have alt text
   90 |     const images = page.locator('img');
   91 |     const imageCount = await images.count();
   92 |     
   93 |     for (let i = 0; i < imageCount; i++) {
   94 |       const img = images.nth(i);
   95 |       const alt = await img.getAttribute('alt');
   96 |       const ariaLabel = await img.getAttribute('aria-label');
   97 |       
   98 |       // Image should have alt text or aria-label
   99 |       expect(alt !== null || ariaLabel !== null).toBe(true);
  100 |     }
  101 |   });
  102 |
  103 |   test('should support keyboard navigation', async ({ page }) => {
  104 |     await page.goto('/security-quizzes');
  105 |     
  106 |     // Test tab navigation
  107 |     await page.keyboard.press('Tab');
  108 |     
  109 |     // Should focus on first focusable element
  110 |     const focusedElement = page.locator(':focus');
```