# Test info

- Name: Quiz Editor >> should save quiz progress automatically
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editor.spec.ts:200:7

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('.quiz-card:first-child a')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editor.spec.ts:203:16
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "My Quizzes" [level=1]
  - link "Create New Quiz":
    - /url: /dashboard/quizzes/create
  - heading "Question Types Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d5de60d5a800e9828815
  - link "Preview":
    - /url: /dashboard/quizzes/6835d5de60d5a800e9828815/preview
  - heading "Validation Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing validation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d5d360d5a800e9828814
  - link "Preview":
    - /url: /dashboard/quizzes/6835d5d360d5a800e9828814/preview
  - heading "Test Quiz for Questions" [level=3]
  - paragraph: Draft
  - paragraph: Testing question creation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d5ce60d5a800e9828813
  - link "Preview":
    - /url: /dashboard/quizzes/6835d5ce60d5a800e9828813/preview
  - heading "Validation Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing validation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d5cd60d5a800e9828812
  - link "Preview":
    - /url: /dashboard/quizzes/6835d5cd60d5a800e9828812/preview
  - heading "Test Quiz" [level=3]
  - paragraph: Draft
  - text: security testing
  - paragraph: This is a test quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d5cc60d5a800e9828811
  - link "Preview":
    - /url: /dashboard/quizzes/6835d5cc60d5a800e9828811/preview
  - heading "Settings Edit Test" [level=3]
  - paragraph: Draft
  - text: updated settings +1
  - paragraph: Testing settings editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880e
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880e/preview
  - heading "Question Addition Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing adding questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2e760d5a800e9828810
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2e760d5a800e9828810/preview
  - heading "Question Deletion Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question deletion
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880f
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880f/preview
  - heading "Question Edit Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880d
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2e660d5a800e982880d/preview
  - heading "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" [level=3]
  - paragraph: Draft
  - text: tag1 tag2 +9
  - paragraph: BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2e060d5a800e982880c
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2e060d5a800e982880c/preview
  - heading "Updated Quiz Title" [level=3]
  - paragraph: Draft
  - paragraph: Updated description with more details
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2dd60d5a800e982880b
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2dd60d5a800e982880b/preview
  - 'heading "Quiz with Special Characters: @#$%^&*()" [level=3]'
  - paragraph: Draft
  - text: special-chars unicode-test +1
  - paragraph: "Description with unicode: 🔒 Security Quiz with émojis and àccénts"
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2da60d5a800e982880a
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2da60d5a800e982880a/preview
  - heading "Tagged Quiz 4" [level=3]
  - paragraph: Draft
  - text: compliance governance +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d760d5a800e9828809
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d760d5a800e9828809/preview
  - heading "Tagged Quiz 3" [level=3]
  - paragraph: Draft
  - text: blue team defense +2
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d660d5a800e9828808
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d660d5a800e9828808/preview
  - heading "Tagged Quiz 2" [level=3]
  - paragraph: Draft
  - text: advanced penetration testing +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d560d5a800e9828807
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d560d5a800e9828807/preview
  - heading "High Standards Quiz (90% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 90% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 90%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d560d5a800e9828806
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d560d5a800e9828806/preview
  - heading "Tagged Quiz 1" [level=3]
  - paragraph: Draft
  - text: security basics +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d360d5a800e9828805
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d360d5a800e9828805/preview
  - heading "High Standards Quiz (80% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 80% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d360d5a800e9828804
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d360d5a800e9828804/preview
  - heading "High Standards Quiz (70% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 70% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d260d5a800e9828803
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d260d5a800e9828803/preview
  - heading "High Standards Quiz (60% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 60% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 60%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d160d5a800e9828802
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d160d5a800e9828802/preview
  - heading "120 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 120 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2d060d5a800e9828801
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2d060d5a800e9828801/preview
  - heading "60 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 60 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2cf60d5a800e9828800
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2cf60d5a800e9828800/preview
  - heading "Malware Analysis Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in malware analysis
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2cd60d5a800e98287ff
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2cd60d5a800e98287ff/preview
  - heading "30 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 30 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2cd60d5a800e98287fe
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2cd60d5a800e98287fe/preview
  - heading "Incident Response Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in incident response
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2cc60d5a800e98287fd
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2cc60d5a800e98287fd/preview
  - heading "10 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 10 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2cc60d5a800e98287fc
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2cc60d5a800e98287fc/preview
  - heading "Cryptography Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in cryptography
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2ca60d5a800e98287fb
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2ca60d5a800e98287fb/preview
  - heading "Network Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in network security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c960d5a800e98287fa
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c960d5a800e98287fa/preview
  - heading "Advanced Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A advanced level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c860d5a800e98287f9
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c860d5a800e98287f9/preview
  - heading "Web Application Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in web application security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c760d5a800e98287f8
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c760d5a800e98287f8/preview
  - heading "Intermediate Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A intermediate level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c660d5a800e98287f7
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c660d5a800e98287f7/preview
  - heading "Beginner Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A beginner level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c460d5a800e98287f6
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c460d5a800e98287f6/preview
  - heading "Advanced Penetration Testing Quiz" [level=3]
  - paragraph: Draft
  - text: penetration testing ethical hacking +1
  - paragraph: Comprehensive quiz covering advanced penetration testing techniques and methodologies
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c260d5a800e98287f5
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c260d5a800e98287f5/preview
  - heading "Basic Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A basic cybersecurity quiz for beginners
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835d2c260d5a800e98287f4
  - link "Preview":
    - /url: /dashboard/quizzes/6835d2c260d5a800e98287f4/preview
  - heading "Responsive Preview Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing responsive preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf9260d5a800e98287f2
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf9260d5a800e98287f2/preview
  - heading "Empty Quiz Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with no questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf9160d5a800e98287f1
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf9160d5a800e98287f1/preview
  - heading "Settings Preview Quiz" [level=3]
  - paragraph: Draft
  - text: preview testing +1
  - paragraph: Quiz with various settings for preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf8f60d5a800e98287f0
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf8f60d5a800e98287f0/preview
  - heading "Exit Preview Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing exit from preview mode
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf8e60d5a800e98287ef
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf8e60d5a800e98287ef/preview
  - heading "Timed Preview Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with time limit for preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf8c60d5a800e98287ee
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf8c60d5a800e98287ee/preview
  - heading "Navigation Preview Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing navigation in preview mode
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf8860d5a800e98287ed
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf8860d5a800e98287ed/preview
  - heading "Multi-Type Question Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf8260d5a800e98287ec
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf8260d5a800e98287ec/preview
  - heading "Single Question Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with one question for preview testing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287ea
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287ea/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287eb
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287eb/preview
  - heading "Test Quiz for Route Debugging" [level=3]
  - paragraph: Draft
  - paragraph: Testing quiz routes
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf7a60d5a800e98287e9
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf7a60d5a800e98287e9/preview
  - heading "Question Types Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf5f60d5a800e98287e8
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf5f60d5a800e98287e8/preview
  - heading "Validation Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing validation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf5860d5a800e98287e7
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf5860d5a800e98287e7/preview
  - heading "Test Quiz for Questions" [level=3]
  - paragraph: Draft
  - paragraph: Testing question creation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf5360d5a800e98287e6
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf5360d5a800e98287e6/preview
  - heading "Test Quiz" [level=3]
  - paragraph: Draft
  - text: security testing
  - paragraph: This is a test quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf5260d5a800e98287e5
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf5260d5a800e98287e5/preview
  - heading "Validation Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing validation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf5160d5a800e98287e4
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf5160d5a800e98287e4/preview
  - heading "Publishing Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing quiz publishing functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4f60d5a800e98287e3
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4f60d5a800e98287e3/preview
  - heading "Question Reorder Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question reordering
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4d60d5a800e98287e2
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4d60d5a800e98287e2/preview
  - heading "Multi-Type Edit Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing editing with different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4c60d5a800e98287e1
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4c60d5a800e98287e1/preview
  - heading "Question Deletion Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question deletion
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4b60d5a800e98287e0
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4b60d5a800e98287e0/preview
  - heading "Question Edit Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4960d5a800e98287df
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4960d5a800e98287df/preview
  - heading "Settings Edit Test" [level=3]
  - paragraph: Draft
  - text: updated settings +1
  - paragraph: Testing settings editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4860d5a800e98287de
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4860d5a800e98287de/preview
  - heading "Updated Quiz Title" [level=3]
  - paragraph: Draft
  - paragraph: Updated description with more details
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4660d5a800e98287dc
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4660d5a800e98287dc/preview
  - heading "Question Addition Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing adding questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4760d5a800e98287dd
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4760d5a800e98287dd/preview
  - 'heading "Quiz with Special Characters: @#$%^&*()" [level=3]'
  - paragraph: Draft
  - text: special-chars unicode-test +1
  - paragraph: "Description with unicode: 🔒 Security Quiz with émojis and àccénts"
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4460d5a800e98287db
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4460d5a800e98287db/preview
  - heading "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" [level=3]
  - paragraph: Draft
  - text: tag1 tag2 +9
  - paragraph: BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4460d5a800e98287da
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4460d5a800e98287da/preview
  - heading "Tagged Quiz 4" [level=3]
  - paragraph: Draft
  - text: compliance governance +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4260d5a800e98287d9
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4260d5a800e98287d9/preview
  - heading "Tagged Quiz 3" [level=3]
  - paragraph: Draft
  - text: blue team defense +2
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4160d5a800e98287d8
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4160d5a800e98287d8/preview
  - heading "Tagged Quiz 2" [level=3]
  - paragraph: Draft
  - text: advanced penetration testing +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4060d5a800e98287d7
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4060d5a800e98287d7/preview
  - heading "Tagged Quiz 1" [level=3]
  - paragraph: Draft
  - text: security basics +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf4060d5a800e98287d6
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf4060d5a800e98287d6/preview
  - heading "120 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 120 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3f60d5a800e98287d5
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3f60d5a800e98287d5/preview
  - heading "60 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 60 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3e60d5a800e98287d4
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3e60d5a800e98287d4/preview
  - heading "High Standards Quiz (90% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 90% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 90%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3e60d5a800e98287d3
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3e60d5a800e98287d3/preview
  - heading "30 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 30 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3d60d5a800e98287d2
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3d60d5a800e98287d2/preview
  - heading "High Standards Quiz (80% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 80% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3d60d5a800e98287d1
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3d60d5a800e98287d1/preview
  - heading "10 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 10 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3c60d5a800e98287d0
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3c60d5a800e98287d0/preview
  - heading "High Standards Quiz (70% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 70% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3c60d5a800e98287cf
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3c60d5a800e98287cf/preview
  - heading "High Standards Quiz (60% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 60% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 60%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3b60d5a800e98287ce
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3b60d5a800e98287ce/preview
  - heading "Malware Analysis Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in malware analysis
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3a60d5a800e98287cd
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3a60d5a800e98287cd/preview
  - heading "Incident Response Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in incident response
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3960d5a800e98287cc
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3960d5a800e98287cc/preview
  - heading "Cryptography Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in cryptography
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3860d5a800e98287cb
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3860d5a800e98287cb/preview
  - heading "Network Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in network security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3860d5a800e98287ca
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3860d5a800e98287ca/preview
  - heading "Web Application Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in web application security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3760d5a800e98287c9
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3760d5a800e98287c9/preview
  - heading "Advanced Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A advanced level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3660d5a800e98287c8
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3660d5a800e98287c8/preview
  - heading "Intermediate Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A intermediate level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3660d5a800e98287c7
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3660d5a800e98287c7/preview
  - heading "Beginner Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A beginner level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3560d5a800e98287c6
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3560d5a800e98287c6/preview
  - heading "Advanced Penetration Testing Quiz" [level=3]
  - paragraph: Draft
  - text: penetration testing ethical hacking +1
  - paragraph: Comprehensive quiz covering advanced penetration testing techniques and methodologies
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3460d5a800e98287c5
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3460d5a800e98287c5/preview
  - heading "Basic Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A basic cybersecurity quiz for beginners
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cf3160d5a800e98287c4
  - link "Preview":
    - /url: /dashboard/quizzes/6835cf3160d5a800e98287c4/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cd212fb0ba26640ef5cc
  - link "Preview":
    - /url: /dashboard/quizzes/6835cd212fb0ba26640ef5cc/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5cb
  - link "Preview":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5cb/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5ca
  - link "Preview":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5ca/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5c8
  - link "Preview":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5c8/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5c9
  - link "Preview":
    - /url: /dashboard/quizzes/6835cd1b2fb0ba26640ef5c9/preview
  - heading "Empty Quiz Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with no questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb70b05ee74a284c8647
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb70b05ee74a284c8647/preview
  - heading "Responsive Preview Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing responsive preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb32b05ee74a284c8646
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb32b05ee74a284c8646/preview
  - heading "Settings Preview Quiz" [level=3]
  - paragraph: Draft
  - text: preview testing +1
  - paragraph: Quiz with various settings for preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb31b05ee74a284c8645
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb31b05ee74a284c8645/preview
  - heading "Exit Preview Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing exit from preview mode
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb30b05ee74a284c8644
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb30b05ee74a284c8644/preview
  - heading "Timed Preview Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with time limit for preview
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb2eb05ee74a284c8643
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb2eb05ee74a284c8643/preview
  - heading "Navigation Preview Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing navigation in preview mode
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb25b05ee74a284c8642
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb25b05ee74a284c8642/preview
  - heading "Multi-Type Question Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb1cb05ee74a284c8641
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb1cb05ee74a284c8641/preview
  - heading "Single Question Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with one question for preview testing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb14b05ee74a284c8640
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb14b05ee74a284c8640/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb11b05ee74a284c863f
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb11b05ee74a284c863f/preview
  - heading "Test Quiz for Route Debugging" [level=3]
  - paragraph: Draft
  - paragraph: Testing quiz routes
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb0db05ee74a284c863e
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb0db05ee74a284c863e/preview
  - heading "Validation Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing validation
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb0bb05ee74a284c863d
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb0bb05ee74a284c863d/preview
  - heading "Publishing Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Testing quiz publishing functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb04b05ee74a284c863c
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb04b05ee74a284c863c/preview
  - heading "Multi-Type Edit Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing editing with different question types
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cb00b05ee74a284c863b
  - link "Preview":
    - /url: /dashboard/quizzes/6835cb00b05ee74a284c863b/preview
  - heading "Question Reorder Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question reordering
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cafcb05ee74a284c863a
  - link "Preview":
    - /url: /dashboard/quizzes/6835cafcb05ee74a284c863a/preview
  - heading "Question Deletion Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question deletion
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835caf9b05ee74a284c8639
  - link "Preview":
    - /url: /dashboard/quizzes/6835caf9b05ee74a284c8639/preview
  - heading "Question Edit Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing question editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835caf7b05ee74a284c8638
  - link "Preview":
    - /url: /dashboard/quizzes/6835caf7b05ee74a284c8638/preview
  - heading "Question Addition Test" [level=3]
  - paragraph: Draft
  - paragraph: Testing adding questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835caeeb05ee74a284c8637
  - link "Preview":
    - /url: /dashboard/quizzes/6835caeeb05ee74a284c8637/preview
  - heading "Settings Edit Test" [level=3]
  - paragraph: Draft
  - text: updated settings +1
  - paragraph: Testing settings editing
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835caebb05ee74a284c8636
  - link "Preview":
    - /url: /dashboard/quizzes/6835caebb05ee74a284c8636/preview
  - heading "Updated Quiz Title" [level=3]
  - paragraph: Draft
  - paragraph: Updated description with more details
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae9b05ee74a284c8635
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae9b05ee74a284c8635/preview
  - heading "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" [level=3]
  - paragraph: Draft
  - text: tag1 tag2 +9
  - paragraph: BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae8b05ee74a284c8634
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae8b05ee74a284c8634/preview
  - 'heading "Quiz with Special Characters: @#$%^&*()" [level=3]'
  - paragraph: Draft
  - text: special-chars unicode-test +1
  - paragraph: "Description with unicode: 🔒 Security Quiz with émojis and àccénts"
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae7b05ee74a284c8633
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae7b05ee74a284c8633/preview
  - heading "Tagged Quiz 4" [level=3]
  - paragraph: Draft
  - text: compliance governance +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae4b05ee74a284c8632
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae4b05ee74a284c8632/preview
  - heading "Tagged Quiz 3" [level=3]
  - paragraph: Draft
  - text: blue team defense +2
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae4b05ee74a284c8631
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae4b05ee74a284c8631/preview
  - heading "Tagged Quiz 2" [level=3]
  - paragraph: Draft
  - text: advanced penetration testing +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae3b05ee74a284c8630
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae3b05ee74a284c8630/preview
  - heading "Tagged Quiz 1" [level=3]
  - paragraph: Draft
  - text: security basics +1
  - paragraph: Quiz with multiple relevant tags
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae3b05ee74a284c862f
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae3b05ee74a284c862f/preview
  - heading "High Standards Quiz (90% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 90% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 90%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae2b05ee74a284c862e
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae2b05ee74a284c862e/preview
  - heading "High Standards Quiz (80% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 80% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 80%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae1b05ee74a284c862d
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae1b05ee74a284c862d/preview
  - heading "High Standards Quiz (70% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 70% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae1b05ee74a284c862c
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae1b05ee74a284c862c/preview
  - heading "High Standards Quiz (60% to pass)" [level=3]
  - paragraph: Draft
  - paragraph: Challenging quiz requiring 60% to pass
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 60%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cae0b05ee74a284c862b
  - link "Preview":
    - /url: /dashboard/quizzes/6835cae0b05ee74a284c862b/preview
  - heading "120 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 120 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadfb05ee74a284c862a
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadfb05ee74a284c862a/preview
  - heading "60 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 60 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadfb05ee74a284c8629
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadfb05ee74a284c8629/preview
  - heading "30 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 30 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadeb05ee74a284c8628
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadeb05ee74a284c8628/preview
  - heading "10 Minute Security Challenge" [level=3]
  - paragraph: Draft
  - paragraph: A timed security quiz with 10 minute limit
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadeb05ee74a284c8627
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadeb05ee74a284c8627/preview
  - heading "Malware Analysis Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in malware analysis
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835caddb05ee74a284c8626
  - link "Preview":
    - /url: /dashboard/quizzes/6835caddb05ee74a284c8626/preview
  - heading "Incident Response Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in incident response
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8625
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8625/preview
  - heading "Cryptography Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in cryptography
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8624
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8624/preview
  - heading "Network Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in network security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8623
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadcb05ee74a284c8623/preview
  - heading "Web Application Security Fundamentals" [level=3]
  - paragraph: Draft
  - paragraph: Essential concepts in web application security
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadbb05ee74a284c8622
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadbb05ee74a284c8622/preview
  - heading "Advanced Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A advanced level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadab05ee74a284c8621
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadab05ee74a284c8621/preview
  - heading "Intermediate Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A intermediate level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cadab05ee74a284c8620
  - link "Preview":
    - /url: /dashboard/quizzes/6835cadab05ee74a284c8620/preview
  - heading "Beginner Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A beginner level cybersecurity quiz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cad9b05ee74a284c861f
  - link "Preview":
    - /url: /dashboard/quizzes/6835cad9b05ee74a284c861f/preview
  - heading "Advanced Penetration Testing Quiz" [level=3]
  - paragraph: Draft
  - text: penetration testing ethical hacking +1
  - paragraph: Comprehensive quiz covering advanced penetration testing techniques and methodologies
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cad9b05ee74a284c861e
  - link "Preview":
    - /url: /dashboard/quizzes/6835cad9b05ee74a284c861e/preview
  - heading "Basic Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A basic cybersecurity quiz for beginners
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cad7b05ee74a284c861d
  - link "Preview":
    - /url: /dashboard/quizzes/6835cad7b05ee74a284c861d/preview
  - heading "Updated Quiz Title" [level=3]
  - paragraph: Draft
  - paragraph: Updated description with more details
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835cab17ca083fe2881d6c4
  - link "Preview":
    - /url: /dashboard/quizzes/6835cab17ca083fe2881d6c4/preview
  - heading "Basic Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A basic cybersecurity quiz for beginners
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835ca98e7eed25c90c7a3f0
  - link "Preview":
    - /url: /dashboard/quizzes/6835ca98e7eed25c90c7a3f0/preview
  - heading "Empty Quiz Preview" [level=3]
  - paragraph: Draft
  - paragraph: Quiz with no questions
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835ca7878a2d5a5f82af20b
  - link "Preview":
    - /url: /dashboard/quizzes/6835ca7878a2d5a5f82af20b/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835c9f69abd4df278cbee04
  - link "Preview":
    - /url: /dashboard/quizzes/6835c9f69abd4df278cbee04/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835c999855115a7ab68ae7c
  - link "Preview":
    - /url: /dashboard/quizzes/6835c999855115a7ab68ae7c/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835af4aeae2930bed92a9d0
  - link "Preview":
    - /url: /dashboard/quizzes/6835af4aeae2930bed92a9d0/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835ab15eae2930bed92a9cf
  - link "Preview":
    - /url: /dashboard/quizzes/6835ab15eae2930bed92a9cf/preview
  - heading "Test Quiz for Route Debugging" [level=3]
  - paragraph: Draft
  - paragraph: Testing quiz routes
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835aac616b943697957c246
  - link "Preview":
    - /url: /dashboard/quizzes/6835aac616b943697957c246/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835aa4beb776c464c06c8a4
  - link "Preview":
    - /url: /dashboard/quizzes/6835aa4beb776c464c06c8a4/preview
  - heading "Debug Quiz" [level=3]
  - paragraph: Draft
  - paragraph: Debug description
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835a944eb776c464c06c8a3
  - link "Preview":
    - /url: /dashboard/quizzes/6835a944eb776c464c06c8a3/preview
  - heading "Advanced Penetration Testing Quiz" [level=3]
  - paragraph: Draft
  - text: penetration testing ethical hacking +1
  - paragraph: Comprehensive quiz covering advanced penetration testing techniques and methodologies
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 85%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835a7d4eb776c464c06c8a2
  - link "Preview":
    - /url: /dashboard/quizzes/6835a7d4eb776c464c06c8a2/preview
  - heading "Updated Quiz Title" [level=3]
  - paragraph: Draft
  - paragraph: Updated description with more details
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835a7b0eb776c464c06c8a1
  - link "Preview":
    - /url: /dashboard/quizzes/6835a7b0eb776c464c06c8a1/preview
  - heading "Preview Test Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A quiz for testing preview functionality
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835a719eb776c464c06c8a0
  - link "Preview":
    - /url: /dashboard/quizzes/6835a719eb776c464c06c8a0/preview
  - heading "Basic Security Quiz" [level=3]
  - paragraph: Draft
  - paragraph: A basic cybersecurity quiz for beginners
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6835a6dbeb776c464c06c89f
  - link "Preview":
    - /url: /dashboard/quizzes/6835a6dbeb776c464c06c89f/preview
  - heading "Testing quizz" [level=3]
  - paragraph: Draft
  - text: basics
  - paragraph: Simple Quizz
  - paragraph: Questions
  - paragraph: "0"
  - paragraph: Passing Score
  - paragraph: 70%
  - text: Updated May 27, 2025
  - link "Edit":
    - /url: /dashboard/quizzes/6834fcbceb776c464c06c89d
  - link "Preview":
    - /url: /dashboard/quizzes/6834fcbceb776c464c06c89d/preview
- alert
```

# Test source

```ts
  103 |     // Verify update was successful
  104 |     await page.waitForSelector('text=Question updated successfully', { timeout: 10000 });
  105 |   });
  106 |
  107 |   test('should delete a question', async ({ page }) => {
  108 |     // Navigate to a quiz with existing questions
  109 |     await page.goto('/dashboard/quizzes');
  110 |
  111 |     // Click on the first quiz
  112 |     await page.click('.quiz-card:first-child a');
  113 |
  114 |     // Navigate to Questions tab
  115 |     await page.click('button:has-text("Questions")');
  116 |
  117 |     // Get initial question count
  118 |     const initialQuestions = await page.locator('.question-item').count();
  119 |
  120 |     // Click delete button on first question
  121 |     await page.click('.question-item:first-child button[aria-label="Delete"]');
  122 |
  123 |     // Confirm deletion in dialog
  124 |     page.on('dialog', dialog => dialog.accept());
  125 |
  126 |     // Verify question was deleted
  127 |     await page.waitForFunction(
  128 |       (expectedCount) => document.querySelectorAll('.question-item').length === expectedCount,
  129 |       initialQuestions - 1
  130 |     );
  131 |   });
  132 |
  133 |   test('should validate required fields', async ({ page }) => {
  134 |     // Create a quiz first
  135 |     await page.goto('/dashboard/quizzes/create');
  136 |     await page.fill('input[id="title"]', 'Validation Test Quiz');
  137 |     await page.fill('textarea[id="description"]', 'Testing validation');
  138 |     await page.click('button:has-text("Create Quiz")');
  139 |
  140 |     // Wait for redirect to editor
  141 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  142 |
  143 |     // Navigate to Questions tab
  144 |     await page.click('button:has-text("Questions")');
  145 |     await page.click('button:has-text("Add Question")');
  146 |
  147 |     // Try to save without filling required fields
  148 |     await page.click('button:has-text("Add Question")');
  149 |
  150 |     // Should show validation errors
  151 |     await expect(page.locator('textarea[id="questionText"]:invalid')).toBeVisible();
  152 |   });
  153 |
  154 |   test('should preview quiz', async ({ page }) => {
  155 |     // Navigate to a quiz
  156 |     await page.goto('/dashboard/quizzes');
  157 |     await page.click('.quiz-card:first-child a');
  158 |
  159 |     // Click preview button
  160 |     await page.click('button:has-text("Preview Quiz")');
  161 |
  162 |     // Should open preview in new tab/window
  163 |     const [previewPage] = await Promise.all([
  164 |       page.waitForEvent('popup'),
  165 |       page.click('button:has-text("Preview Quiz")')
  166 |     ]);
  167 |
  168 |     // Verify preview page loaded
  169 |     await expect(previewPage.locator('h1')).toBeVisible();
  170 |   });
  171 |
  172 |   test('should handle different question types', async ({ page }) => {
  173 |     // Create a quiz first
  174 |     await page.goto('/dashboard/quizzes/create');
  175 |     await page.fill('input[id="title"]', 'Question Types Test');
  176 |     await page.fill('textarea[id="description"]', 'Testing different question types');
  177 |     await page.click('button:has-text("Create Quiz")');
  178 |
  179 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  180 |     await page.click('button:has-text("Questions")');
  181 |     await page.click('button:has-text("Add Question")');
  182 |
  183 |     // Test True/False question
  184 |     await page.selectOption('select[id="questionType"]', 'true_false');
  185 |     await page.fill('textarea[id="questionText"]', 'Is this a true/false question?');
  186 |     await page.click('input[value="true"]');
  187 |     await page.click('button:has-text("Add Question")');
  188 |
  189 |     await page.waitForSelector('text=Question added successfully');
  190 |
  191 |     // Test Short Answer question
  192 |     await page.selectOption('select[id="questionType"]', 'short_answer');
  193 |     await page.fill('textarea[id="questionText"]', 'What is your name?');
  194 |     await page.fill('input[placeholder="Enter correct answer"]', 'John Doe');
  195 |     await page.click('button:has-text("Add Question")');
  196 |
  197 |     await page.waitForSelector('text=Question added successfully');
  198 |   });
  199 |
  200 |   test('should save quiz progress automatically', async ({ page }) => {
  201 |     // Navigate to a quiz with questions
  202 |     await page.goto('/dashboard/quizzes');
> 203 |     await page.click('.quiz-card:first-child a');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
  204 |
  205 |     // Make changes to quiz metadata
  206 |     await page.fill('input[id="title"]', 'Updated Quiz Title');
  207 |
  208 |     // Wait for auto-save indicator
  209 |     await page.waitForSelector('text=Saved', { timeout: 5000 });
  210 |
  211 |     // Refresh page to verify changes were saved
  212 |     await page.reload();
  213 |     await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
  214 |   });
  215 |
  216 |   test('should handle quiz publishing', async ({ page }) => {
  217 |     // Navigate to a quiz
  218 |     await page.goto('/dashboard/quizzes');
  219 |     await page.click('.quiz-card:first-child a');
  220 |
  221 |     // Publish the quiz
  222 |     await page.click('button:has-text("Publish Quiz")');
  223 |
  224 |     // Confirm publishing
  225 |     await page.click('button:has-text("Confirm")');
  226 |
  227 |     // Should show published status
  228 |     await expect(page.locator('text=Published')).toBeVisible();
  229 |   });
  230 | });
  231 |
```