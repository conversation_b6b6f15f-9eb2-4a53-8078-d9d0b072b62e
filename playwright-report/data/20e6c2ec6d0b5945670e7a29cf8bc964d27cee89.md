# Test info

- Name: Dashboard >> should navigate to different dashboard sections
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:35:7

# Error details

```
Error: page.click: Unexpected token "=" while parsing css selector "a[href="/dashboard/quizzes"], text=Quizzes". Did you mean to CSS.escape it?
Call log:
  - waiting for a[href="/dashboard/quizzes"], text=Quizzes

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/dashboard.spec.ts:37:16
```

# Page snapshot

```yaml
- alert
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: Quiz<PERSON><NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Dashboard" [level=1]
  - heading "Welcome, QuizFlow Admin!" [level=3]
  - paragraph: Manage quizzes and view your progress
  - link "Create New Quiz":
    - /url: /dashboard/quizzes/create
  - link "View All Quizzes":
    - /url: /dashboard/quizzes
  - link "Explore Quizzes":
    - /url: /explore
  - heading "Your Stats" [level=3]
  - paragraph: Your quiz activity and performance
  - paragraph: Created Quizzes
  - paragraph: "5"
  - paragraph: Completed Quizzes
  - paragraph: "0"
  - heading "Your Quizzes" [level=3]
  - paragraph: Recently created and updated quizzes
  - list:
    - listitem:
      - paragraph: Responsive Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf9260d5a800e98287f2
    - listitem:
      - paragraph: Empty Quiz Preview
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf9160d5a800e98287f1
    - listitem:
      - paragraph: Settings Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8f60d5a800e98287f0
    - listitem:
      - paragraph: Exit Preview Test
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8e60d5a800e98287ef
    - listitem:
      - paragraph: Timed Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8c60d5a800e98287ee
  - link "View all quizzes":
    - /url: /dashboard/quizzes
  - heading "Recent Activity" [level=3]
  - paragraph: Your recent quiz attempts
  - paragraph: You haven't taken any quizzes yet.
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Dashboard', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as admin user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'admin123');
   9 |     await page.click('button[type="submit"]');
   10 |     await page.waitForURL('/dashboard');
   11 |   });
   12 |
   13 |   test('should display dashboard overview', async ({ page }) => {
   14 |     // Should show main dashboard elements
   15 |     await expect(page.locator('h1')).toBeVisible();
   16 |
   17 |     // Should show some form of navigation
   18 |     const navigation = page.locator('nav, .navigation, .nav-menu, a[href*="dashboard"]');
   19 |     const navCount = await navigation.count();
   20 |     expect(navCount).toBeGreaterThan(0);
   21 |
   22 |     // Should show dashboard content (any content is fine)
   23 |     const content = page.locator('main, .main-content, .dashboard-content, .content');
   24 |     const contentCount = await content.count();
   25 |     if (contentCount === 0) {
   26 |       // Fallback: check for any visible content
   27 |       const anyContent = page.locator('body *:visible');
   28 |       const anyContentCount = await anyContent.count();
   29 |       expect(anyContentCount).toBeGreaterThan(5); // Should have some content
   30 |     } else {
   31 |       expect(contentCount).toBeGreaterThan(0);
   32 |     }
   33 |   });
   34 |
   35 |   test('should navigate to different dashboard sections', async ({ page }) => {
   36 |     // Test navigation to quizzes
>  37 |     await page.click('a[href="/dashboard/quizzes"], text=Quizzes');
      |                ^ Error: page.click: Unexpected token "=" while parsing css selector "a[href="/dashboard/quizzes"], text=Quizzes". Did you mean to CSS.escape it?
   38 |     await page.waitForURL('/dashboard/quizzes');
   39 |     await expect(page.locator('h1')).toContainText('Quizzes');
   40 |
   41 |     // Test navigation to analytics
   42 |     await page.click('a[href="/dashboard/analytics"], text=Analytics');
   43 |     await page.waitForURL('/dashboard/analytics');
   44 |     await expect(page.locator('h1')).toContainText('Analytics');
   45 |
   46 |     // Test navigation to admin section (admin only)
   47 |     const adminLink = page.locator('a[href="/dashboard/admin"], text=Admin');
   48 |     if (await adminLink.isVisible()) {
   49 |       await adminLink.click();
   50 |       await page.waitForURL('/dashboard/admin');
   51 |       await expect(page.locator('h1')).toContainText('Admin');
   52 |     }
   53 |   });
   54 |
   55 |   test('should display user profile information', async ({ page }) => {
   56 |     // Look for user profile section
   57 |     const profileSection = page.locator('.user-profile, .profile-info, [data-testid="user-profile"]');
   58 |     if (await profileSection.isVisible()) {
   59 |       await expect(profileSection).toContainText('<EMAIL>');
   60 |     }
   61 |
   62 |     // Check for user menu or dropdown
   63 |     const userMenu = page.locator('.user-menu, .profile-dropdown, button:has-text("admin")');
   64 |     if (await userMenu.isVisible()) {
   65 |       await userMenu.click();
   66 |       await expect(page.locator('text=Logout, text=Sign out')).toBeVisible();
   67 |     }
   68 |   });
   69 |
   70 |   test('should show recent activity', async ({ page }) => {
   71 |     // Look for recent activity section
   72 |     const activitySection = page.locator('.recent-activity, .activity-feed, [data-testid="recent-activity"]');
   73 |     if (await activitySection.isVisible()) {
   74 |       await expect(activitySection).toBeVisible();
   75 |
   76 |       // Should show some activity items
   77 |       const activityItems = page.locator('.activity-item, .activity-entry');
   78 |       const activityCount = await activityItems.count();
   79 |       if (activityCount > 0) {
   80 |         await expect(activityItems.first()).toBeVisible();
   81 |       }
   82 |     }
   83 |   });
   84 |
   85 |   test('should display quiz statistics', async ({ page }) => {
   86 |     // Look for quiz statistics
   87 |     const statsElements = page.locator('.quiz-stats, .statistics, .metrics');
   88 |     if (await statsElements.count() > 0) {
   89 |       await expect(statsElements.first()).toBeVisible();
   90 |
   91 |       // Should show numbers or charts
   92 |       const numbers = page.locator('text=/\\d+/');
   93 |       await expect(numbers.first()).toBeVisible();
   94 |     }
   95 |   });
   96 |
   97 |   test('should handle responsive design', async ({ page }) => {
   98 |     // Test mobile viewport
   99 |     await page.setViewportSize({ width: 375, height: 667 });
  100 |     await page.reload();
  101 |
  102 |     // Should still show main content
  103 |     await expect(page.locator('h1')).toBeVisible();
  104 |
  105 |     // Mobile menu should be accessible
  106 |     const mobileMenu = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"]');
  107 |     if (await mobileMenu.isVisible()) {
  108 |       await mobileMenu.click();
  109 |       await expect(page.locator('nav, .navigation')).toBeVisible();
  110 |     }
  111 |
  112 |     // Reset to desktop
  113 |     await page.setViewportSize({ width: 1280, height: 720 });
  114 |   });
  115 |
  116 |   test('should search functionality', async ({ page }) => {
  117 |     // Look for search input
  118 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
  119 |     if (await searchInput.isVisible()) {
  120 |       await searchInput.fill('security');
  121 |       await searchInput.press('Enter');
  122 |
  123 |       // Should show search results or filter content
  124 |       await page.waitForTimeout(1000); // Wait for search to process
  125 |
  126 |       // Verify search worked (results or filtered content)
  127 |       const results = page.locator('.search-results, .filtered-content');
  128 |       if (await results.isVisible()) {
  129 |         await expect(results).toBeVisible();
  130 |       }
  131 |     }
  132 |   });
  133 |
  134 |   test('should handle notifications', async ({ page }) => {
  135 |     // Look for notification bell or indicator
  136 |     const notificationBell = page.locator('.notification-bell, .notifications, [data-testid="notifications"]');
  137 |     if (await notificationBell.isVisible()) {
```