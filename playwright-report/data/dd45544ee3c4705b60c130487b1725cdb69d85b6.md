# Test info

- Name: Quiz Editing - All Possibilities >> should handle quiz publishing and unpublishing
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:331:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=Published, .published-status')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=Published, .published-status')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:371:71
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Edit Quiz" [level=1]
  - button "Back to Quizzes"
  - button "Preview Quiz"
  - tablist:
    - tab "Quiz Details"
    - tab "Questions"
    - tab "Question Pools"
    - tab "Publish" [selected]
  - tabpanel "Publish":
    - heading "Publish Settings" [level=3]
    - paragraph: Control the visibility and availability of your quiz
    - heading "Current Status" [level=3]
    - paragraph: Draft - Only you can see this quiz
    - button "Publish Quiz"
    - heading "Publishing Checklist" [level=3]
    - list:
      - listitem:
        - text: ✓
        - paragraph: Quiz Title
        - paragraph: Title is set
      - listitem:
        - text: "!"
        - paragraph: Questions
        - paragraph: Quiz needs at least one question
      - listitem:
        - text: ✓
        - paragraph: Passing Score
        - paragraph: Passing score is set to 70%
      - listitem:
        - text: ✓
        - paragraph: Time Limit
        - paragraph: Time limit is set to 15 minutes
- alert
```

# Test source

```ts
  271 |         await moveUpBtn.nth(1).click();
  272 |         await page.waitForTimeout(1000);
  273 |       }
  274 |       
  275 |       // Verify order changed (this is a basic check)
  276 |       const questionTexts = await page.locator('.question-text, .question-title').allTextContents();
  277 |       expect(questionTexts.length).toBeGreaterThan(0);
  278 |     }
  279 |   });
  280 |
  281 |   test('should edit quiz with different question types', async ({ page }) => {
  282 |     // Create quiz and add various question types
  283 |     await page.goto('/dashboard/quizzes/create');
  284 |     await page.fill('input[id="title"]', 'Multi-Type Edit Test');
  285 |     await page.fill('textarea[id="description"]', 'Testing editing with different question types');
  286 |     await page.click('button[type="submit"]');
  287 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  288 |     
  289 |     await page.click('button:has-text("Questions")');
  290 |     
  291 |     const questionTypes = ['multiple_choice', 'true_false', 'short_answer'];
  292 |     
  293 |     for (let i = 0; i < questionTypes.length; i++) {
  294 |       await page.click('button:has-text("Add Question")');
  295 |       
  296 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  297 |       if (await questionTypeSelect.isVisible()) {
  298 |         await questionTypeSelect.selectOption(questionTypes[i]);
  299 |         await page.fill('textarea[id="questionText"]', `${questionTypes[i]} question for editing`);
  300 |         
  301 |         if (questionTypes[i] === 'multiple_choice') {
  302 |           const optionInputs = page.locator('input[placeholder*="option"]');
  303 |           if (await optionInputs.count() >= 2) {
  304 |             await optionInputs.nth(0).fill('MC Option A');
  305 |             await optionInputs.nth(1).fill('MC Option B');
  306 |           }
  307 |         } else if (questionTypes[i] === 'true_false') {
  308 |           const trueFalseOptions = page.locator('input[value="true"]');
  309 |           if (await trueFalseOptions.count() > 0) {
  310 |             await trueFalseOptions.first().check();
  311 |           }
  312 |         } else if (questionTypes[i] === 'short_answer') {
  313 |           const answerInput = page.locator('input[placeholder*="answer"], textarea[placeholder*="answer"]');
  314 |           if (await answerInput.isVisible()) {
  315 |             await answerInput.fill('Sample correct answer');
  316 |           }
  317 |         }
  318 |         
  319 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  320 |         await page.waitForTimeout(1000);
  321 |       }
  322 |     }
  323 |     
  324 |     // Verify all question types were added
  325 |     await page.click('button:has-text("Existing Questions")');
  326 |     await expect(page.locator('text=multiple_choice question')).toBeVisible();
  327 |     await expect(page.locator('text=true_false question')).toBeVisible();
  328 |     await expect(page.locator('text=short_answer question')).toBeVisible();
  329 |   });
  330 |
  331 |   test('should handle quiz publishing and unpublishing', async ({ page }) => {
  332 |     // Create a complete quiz
  333 |     await page.goto('/dashboard/quizzes/create');
  334 |     await page.fill('input[id="title"]', 'Publishing Test Quiz');
  335 |     await page.fill('textarea[id="description"]', 'Testing quiz publishing functionality');
  336 |     await page.click('button[type="submit"]');
  337 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  338 |     
  339 |     // Add at least one question
  340 |     await page.click('button:has-text("Questions")');
  341 |     await page.click('button:has-text("Add Question")');
  342 |     
  343 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  344 |     if (await questionTypeSelect.isVisible()) {
  345 |       await questionTypeSelect.selectOption('multiple_choice');
  346 |       await page.fill('textarea[id="questionText"]', 'Ready to publish question');
  347 |       
  348 |       const optionInputs = page.locator('input[placeholder*="option"]');
  349 |       if (await optionInputs.count() >= 2) {
  350 |         await optionInputs.nth(0).fill('Publish Option A');
  351 |         await optionInputs.nth(1).fill('Publish Option B');
  352 |       }
  353 |       
  354 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  355 |       await page.waitForTimeout(1000);
  356 |     }
  357 |     
  358 |     // Test publishing
  359 |     const publishBtn = page.locator('button:has-text("Publish"), .publish-quiz');
  360 |     if (await publishBtn.isVisible()) {
  361 |       await publishBtn.click();
  362 |       
  363 |       // Confirm publishing if confirmation dialog appears
  364 |       const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
  365 |       if (await confirmBtn.isVisible()) {
  366 |         await confirmBtn.click();
  367 |         await page.waitForTimeout(1000);
  368 |       }
  369 |       
  370 |       // Should show published status
> 371 |       await expect(page.locator('text=Published, .published-status')).toBeVisible();
      |                                                                       ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  372 |       
  373 |       // Test unpublishing
  374 |       const unpublishBtn = page.locator('button:has-text("Unpublish"), .unpublish-quiz');
  375 |       if (await unpublishBtn.isVisible()) {
  376 |         await unpublishBtn.click();
  377 |         
  378 |         const confirmUnpublish = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
  379 |         if (await confirmUnpublish.isVisible()) {
  380 |           await confirmUnpublish.click();
  381 |           await page.waitForTimeout(1000);
  382 |         }
  383 |         
  384 |         // Should show draft status
  385 |         await expect(page.locator('text=Draft, .draft-status')).toBeVisible();
  386 |       }
  387 |     }
  388 |   });
  389 |
  390 |   test('should validate quiz before saving', async ({ page }) => {
  391 |     // Create quiz and test validation
  392 |     await page.goto('/dashboard/quizzes/create');
  393 |     await page.fill('input[id="title"]', 'Validation Test Quiz');
  394 |     await page.fill('textarea[id="description"]', 'Testing validation');
  395 |     await page.click('button[type="submit"]');
  396 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  397 |     
  398 |     // Try to clear required fields and save
  399 |     await page.fill('input[id="title"]', '');
  400 |     
  401 |     const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
  402 |     if (await saveBtn.isVisible()) {
  403 |       await saveBtn.click();
  404 |       
  405 |       // Should show validation error
  406 |       const titleInput = page.locator('input[id="title"]');
  407 |       const isValid = await titleInput.evaluate(el => (el as HTMLInputElement).validity.valid);
  408 |       expect(isValid).toBe(false);
  409 |     }
  410 |     
  411 |     // Test invalid time limit
  412 |     await page.fill('input[id="title"]', 'Valid Title');
  413 |     await page.fill('input[id="timeLimit"]', '-5');
  414 |     
  415 |     if (await saveBtn.isVisible()) {
  416 |       await saveBtn.click();
  417 |       
  418 |       // Should show validation error for negative time
  419 |       const timeLimitInput = page.locator('input[id="timeLimit"]');
  420 |       const timeValid = await timeLimitInput.evaluate(el => (el as HTMLInputElement).validity.valid);
  421 |       expect(timeValid).toBe(false);
  422 |     }
  423 |   });
  424 | });
  425 |
```