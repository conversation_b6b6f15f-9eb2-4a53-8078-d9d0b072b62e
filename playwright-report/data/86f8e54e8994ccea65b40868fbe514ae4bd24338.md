# Test info

- Name: Authentication >> should allow admin login
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:18:7

# Error details

```
Error: page.fill: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('input[name="email"]')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:22:16
```

# Page snapshot

```yaml
- heading "404" [level=1]
- heading "This page could not be found." [level=2]
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Authentication', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Clear any existing sessions
   6 |     await page.context().clearCookies();
   7 |   });
   8 |
   9 |   test('should redirect unauthenticated users to login', async ({ page }) => {
   10 |     // Try to access protected dashboard
   11 |     await page.goto('/dashboard');
   12 |     
   13 |     // Should redirect to sign in page
   14 |     await page.waitForURL('/auth/signin');
   15 |     expect(page.url()).toContain('/auth/signin');
   16 |   });
   17 |
   18 |   test('should allow admin login', async ({ page }) => {
   19 |     await page.goto('/auth/signin');
   20 |     
   21 |     // Fill in admin credentials
>  22 |     await page.fill('input[name="email"]', '<EMAIL>');
      |                ^ Error: page.fill: Test timeout of 30000ms exceeded.
   23 |     await page.fill('input[name="password"]', 'admin123');
   24 |     
   25 |     // Submit form
   26 |     await page.click('button[type="submit"]');
   27 |     
   28 |     // Should redirect to dashboard
   29 |     await page.waitForURL('/dashboard');
   30 |     expect(page.url()).toContain('/dashboard');
   31 |     
   32 |     // Should show admin features
   33 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
   34 |   });
   35 |
   36 |   test('should allow user login', async ({ page }) => {
   37 |     await page.goto('/auth/signin');
   38 |     
   39 |     // Fill in user credentials
   40 |     await page.fill('input[name="email"]', '<EMAIL>');
   41 |     await page.fill('input[name="password"]', 'user123');
   42 |     
   43 |     // Submit form
   44 |     await page.click('button[type="submit"]');
   45 |     
   46 |     // Should redirect to dashboard
   47 |     await page.waitForURL('/dashboard');
   48 |     expect(page.url()).toContain('/dashboard');
   49 |     
   50 |     // Should NOT show admin features
   51 |     await expect(page.locator('text=Create Quiz')).not.toBeVisible();
   52 |   });
   53 |
   54 |   test('should show error for invalid credentials', async ({ page }) => {
   55 |     await page.goto('/auth/signin');
   56 |     
   57 |     // Fill in invalid credentials
   58 |     await page.fill('input[name="email"]', '<EMAIL>');
   59 |     await page.fill('input[name="password"]', 'wrongpassword');
   60 |     
   61 |     // Submit form
   62 |     await page.click('button[type="submit"]');
   63 |     
   64 |     // Should show error message
   65 |     await expect(page.locator('text=Invalid credentials')).toBeVisible();
   66 |     
   67 |     // Should stay on sign in page
   68 |     expect(page.url()).toContain('/auth/signin');
   69 |   });
   70 |
   71 |   test('should allow user registration', async ({ page }) => {
   72 |     await page.goto('/auth/signup');
   73 |     
   74 |     // Fill in registration form
   75 |     await page.fill('input[name="name"]', 'Test User');
   76 |     await page.fill('input[name="email"]', `test${Date.now()}@example.com`);
   77 |     await page.fill('input[name="password"]', 'testpassword123');
   78 |     await page.fill('input[name="confirmPassword"]', 'testpassword123');
   79 |     
   80 |     // Submit form
   81 |     await page.click('button[type="submit"]');
   82 |     
   83 |     // Should redirect to dashboard or show success message
   84 |     await page.waitForURL('/dashboard');
   85 |     expect(page.url()).toContain('/dashboard');
   86 |   });
   87 |
   88 |   test('should validate registration form', async ({ page }) => {
   89 |     await page.goto('/auth/signup');
   90 |     
   91 |     // Try to submit empty form
   92 |     await page.click('button[type="submit"]');
   93 |     
   94 |     // Should show validation errors
   95 |     await expect(page.locator('input[name="email"]:invalid')).toBeVisible();
   96 |     await expect(page.locator('input[name="password"]:invalid')).toBeVisible();
   97 |   });
   98 |
   99 |   test('should validate password confirmation', async ({ page }) => {
  100 |     await page.goto('/auth/signup');
  101 |     
  102 |     // Fill in mismatched passwords
  103 |     await page.fill('input[name="name"]', 'Test User');
  104 |     await page.fill('input[name="email"]', '<EMAIL>');
  105 |     await page.fill('input[name="password"]', 'password123');
  106 |     await page.fill('input[name="confirmPassword"]', 'differentpassword');
  107 |     
  108 |     // Submit form
  109 |     await page.click('button[type="submit"]');
  110 |     
  111 |     // Should show password mismatch error
  112 |     await expect(page.locator('text=Passwords do not match')).toBeVisible();
  113 |   });
  114 |
  115 |   test('should allow logout', async ({ page }) => {
  116 |     // Login first
  117 |     await page.goto('/auth/signin');
  118 |     await page.fill('input[name="email"]', '<EMAIL>');
  119 |     await page.fill('input[name="password"]', 'admin123');
  120 |     await page.click('button[type="submit"]');
  121 |     
  122 |     await page.waitForURL('/dashboard');
```