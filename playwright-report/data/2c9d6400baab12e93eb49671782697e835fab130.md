# Test info

- Name: <PERSON>rro<PERSON>ling >> should handle permission errors
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:194:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:200:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  100 |       await expect(validationErrors.first()).toBeVisible();
  101 |     }
  102 |     
  103 |     // Test password mismatch
  104 |     await page.fill('input[id="name"]', 'Test User');
  105 |     await page.fill('input[id="email"]', '<EMAIL>');
  106 |     await page.fill('input[id="password"]', 'password123');
  107 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
  108 |     await page.click('button[type="submit"]');
  109 |     
  110 |     // Should show password mismatch error
  111 |     const passwordError = page.locator('text=Passwords do not match, text=password, .password-error');
  112 |     if (await passwordError.count() > 0) {
  113 |       await expect(passwordError.first()).toBeVisible();
  114 |     }
  115 |   });
  116 |
  117 |   test('should handle quiz loading errors', async ({ page }) => {
  118 |     // Login first
  119 |     await page.goto('/auth/login');
  120 |     await page.fill('input[id="email"]', '<EMAIL>');
  121 |     await page.fill('input[id="password"]', 'user123');
  122 |     await page.click('button[type="submit"]');
  123 |     await page.waitForURL('/dashboard');
  124 |     
  125 |     // Simulate quiz loading error
  126 |     await page.route('**/api/quizzes/**', route => route.abort());
  127 |     
  128 |     await page.goto('/security-quizzes');
  129 |     
  130 |     // Should show error state
  131 |     const errorState = page.locator('.error-state, .loading-error, text=failed to load, text=error loading');
  132 |     if (await errorState.count() > 0) {
  133 |       await expect(errorState.first()).toBeVisible();
  134 |     }
  135 |   });
  136 |
  137 |   test('should handle file upload errors', async ({ page }) => {
  138 |     // Login as admin
  139 |     await page.goto('/auth/login');
  140 |     await page.fill('input[id="email"]', '<EMAIL>');
  141 |     await page.fill('input[id="password"]', 'admin123');
  142 |     await page.click('button[type="submit"]');
  143 |     await page.waitForURL('/dashboard');
  144 |     
  145 |     // Navigate to quiz creation
  146 |     await page.goto('/dashboard/quizzes/create');
  147 |     
  148 |     // Look for file upload input
  149 |     const fileInput = page.locator('input[type="file"]');
  150 |     if (await fileInput.isVisible()) {
  151 |       // Simulate upload error
  152 |       await page.route('**/api/upload/**', route => {
  153 |         route.fulfill({
  154 |           status: 413,
  155 |           contentType: 'application/json',
  156 |           body: JSON.stringify({ error: 'File too large' })
  157 |         });
  158 |       });
  159 |       
  160 |       // Try to upload a file
  161 |       await fileInput.setInputFiles({
  162 |         name: 'test.txt',
  163 |         mimeType: 'text/plain',
  164 |         buffer: Buffer.from('test content')
  165 |       });
  166 |       
  167 |       // Should show upload error
  168 |       const uploadError = page.locator('.upload-error, .file-error, text=file too large, text=upload failed');
  169 |       if (await uploadError.count() > 0) {
  170 |         await expect(uploadError.first()).toBeVisible();
  171 |       }
  172 |     }
  173 |   });
  174 |
  175 |   test('should handle database connection errors', async ({ page }) => {
  176 |     // Simulate database error
  177 |     await page.route('**/api/**', route => {
  178 |       route.fulfill({
  179 |         status: 503,
  180 |         contentType: 'application/json',
  181 |         body: JSON.stringify({ error: 'Service Unavailable' })
  182 |       });
  183 |     });
  184 |     
  185 |     await page.goto('/security-quizzes');
  186 |     
  187 |     // Should show service unavailable message
  188 |     const serviceError = page.locator('.service-error, text=service unavailable, text=temporarily unavailable');
  189 |     if (await serviceError.count() > 0) {
  190 |       await expect(serviceError.first()).toBeVisible();
  191 |     }
  192 |   });
  193 |
  194 |   test('should handle permission errors', async ({ page }) => {
  195 |     // Login as regular user
  196 |     await page.goto('/auth/login');
  197 |     await page.fill('input[id="email"]', '<EMAIL>');
  198 |     await page.fill('input[id="password"]', 'user123');
  199 |     await page.click('button[type="submit"]');
> 200 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  201 |     
  202 |     // Try to access admin area
  203 |     await page.goto('/dashboard/admin');
  204 |     
  205 |     // Should show access denied or redirect
  206 |     const accessDenied = page.locator('text=Access denied, text=Unauthorized, text=Permission denied');
  207 |     const isRedirected = !page.url().includes('/admin');
  208 |     
  209 |     expect(await accessDenied.count() > 0 || isRedirected).toBe(true);
  210 |   });
  211 |
  212 |   test('should handle timeout errors', async ({ page }) => {
  213 |     // Simulate slow response
  214 |     await page.route('**/api/**', async route => {
  215 |       await new Promise(resolve => setTimeout(resolve, 10000)); // 10 second delay
  216 |       route.continue();
  217 |     });
  218 |     
  219 |     // Set shorter timeout for this test
  220 |     page.setDefaultTimeout(5000);
  221 |     
  222 |     try {
  223 |       await page.goto('/auth/login');
  224 |       await page.fill('input[id="email"]', '<EMAIL>');
  225 |       await page.fill('input[id="password"]', 'user123');
  226 |       await page.click('button[type="submit"]');
  227 |       
  228 |       // Should timeout and show error
  229 |       await page.waitForSelector('.timeout-error, .loading-error', { timeout: 6000 });
  230 |     } catch (error) {
  231 |       // Timeout is expected behavior
  232 |       expect(error.message).toContain('timeout');
  233 |     }
  234 |   });
  235 |
  236 |   test('should handle JavaScript errors gracefully', async ({ page }) => {
  237 |     // Listen for console errors
  238 |     const consoleErrors: string[] = [];
  239 |     page.on('console', msg => {
  240 |       if (msg.type() === 'error') {
  241 |         consoleErrors.push(msg.text());
  242 |       }
  243 |     });
  244 |     
  245 |     // Listen for page errors
  246 |     const pageErrors: string[] = [];
  247 |     page.on('pageerror', error => {
  248 |       pageErrors.push(error.message);
  249 |     });
  250 |     
  251 |     await page.goto('/security-quizzes');
  252 |     
  253 |     // Inject a JavaScript error
  254 |     await page.evaluate(() => {
  255 |       // This should cause an error but not break the page
  256 |       try {
  257 |         (window as any).nonExistentFunction();
  258 |       } catch (e) {
  259 |         console.error('Test error:', e);
  260 |       }
  261 |     });
  262 |     
  263 |     // Page should still be functional
  264 |     await expect(page.locator('h1')).toBeVisible();
  265 |     
  266 |     // Should have caught the error
  267 |     expect(consoleErrors.length).toBeGreaterThan(0);
  268 |   });
  269 |
  270 |   test('should provide error recovery options', async ({ page }) => {
  271 |     // Simulate network error
  272 |     await page.route('**/api/quizzes', route => route.abort());
  273 |     
  274 |     await page.goto('/security-quizzes');
  275 |     
  276 |     // Look for retry button
  277 |     const retryButton = page.locator('button:has-text("Retry"), button:has-text("Try Again"), .retry-button');
  278 |     if (await retryButton.isVisible()) {
  279 |       // Remove network error simulation
  280 |       await page.unroute('**/api/quizzes');
  281 |       
  282 |       // Click retry
  283 |       await retryButton.click();
  284 |       
  285 |       // Should recover and show content
  286 |       await page.waitForTimeout(2000);
  287 |       const quizCards = page.locator('.quiz-card, .quiz-item');
  288 |       if (await quizCards.count() > 0) {
  289 |         await expect(quizCards.first()).toBeVisible();
  290 |       }
  291 |     }
  292 |   });
  293 |
  294 |   test('should handle malformed data gracefully', async ({ page }) => {
  295 |     // Simulate malformed API response
  296 |     await page.route('**/api/quizzes', route => {
  297 |       route.fulfill({
  298 |         status: 200,
  299 |         contentType: 'application/json',
  300 |         body: '{"invalid": json malformed'
```