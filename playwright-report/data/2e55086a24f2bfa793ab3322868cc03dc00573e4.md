# Test info

- Name: Quiz Preview Functionality >> should preview quiz responsively
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:495:7

# Error details

```
Error: locator.click: Test timeout of 60000ms exceeded.
Call log:
  - waiting for locator('button:has-text("Preview")')
    - locator resolved to <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">Preview Quiz</button>
  - attempting click action
    - waiting for element to be visible, enabled and stable
    - element is visible, enabled and stable
    - scrolling into view if needed
    - done scrolling
  - element was detached from the DOM, retrying

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:534:26
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Quiz Preview" [level=1]
  - paragraph: Preview how your quiz will appear to users
  - link "Back to Edit":
    - /url: /dashboard/quizzes/6835cb32b05ee74a284c8646/edit
  - heading "Responsive Preview Quiz" [level=3]
  - paragraph: Testing responsive preview
  - text: Draft 0 Questions 15 minutes 70% to pass Tags available
  - img
  - heading "Preview Mode" [level=3]
  - paragraph: This is how your quiz will appear to users. Your answers won't be saved, and no scores will be recorded.
  - heading "No Questions Added" [level=3]
  - paragraph: Add some questions to your quiz to see the preview.
  - link "Add Questions":
    - /url: /dashboard/quizzes/6835cb32b05ee74a284c8646/edit?tab=questions
- alert
```

# Test source

```ts
  434 |       if (await timerElements.count() > 0) {
  435 |         await expect(timerElements.first()).toBeVisible();
  436 |       }
  437 |     }
  438 |   });
  439 |
  440 |   test('should exit preview mode', async ({ page }) => {
  441 |     // Create and preview a quiz
  442 |     await page.goto('/dashboard/quizzes/create');
  443 |     await page.fill('input[id="title"]', 'Exit Preview Test');
  444 |     await page.fill('textarea[id="description"]', 'Testing exit from preview mode');
  445 |     await page.click('button[type="submit"]');
  446 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  447 |
  448 |     // Enter preview mode
  449 |     const previewBtn = page.locator('button:has-text("Preview")');
  450 |     if (await previewBtn.isVisible()) {
  451 |       await previewBtn.click();
  452 |
  453 |       // Look for exit/close preview button
  454 |       const exitBtn = page.locator('button:has-text("Exit"), button:has-text("Close"), button:has-text("Back to Edit"), .exit-preview');
  455 |       if (await exitBtn.isVisible()) {
  456 |         await exitBtn.click();
  457 |
  458 |         // Should return to edit mode
  459 |         await expect(page.locator('input[id="title"]')).toBeVisible();
  460 |         await expect(page.locator('input[id="title"]')).toHaveValue('Exit Preview Test');
  461 |       }
  462 |     }
  463 |   });
  464 |
  465 |   test('should preview quiz with different settings', async ({ page }) => {
  466 |     // Create quiz with various settings
  467 |     await page.goto('/dashboard/quizzes/create');
  468 |     await page.fill('input[id="title"]', 'Settings Preview Quiz');
  469 |     await page.fill('textarea[id="description"]', 'Quiz with various settings for preview');
  470 |     await page.fill('input[id="passingScore"]', '80');
  471 |     await page.fill('input[id="timeLimit"]', '30');
  472 |     await page.fill('input[id="tags"]', 'preview, testing, settings');
  473 |     await page.click('button[type="submit"]');
  474 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  475 |
  476 |     // Preview the quiz
  477 |     const previewBtn = page.locator('button:has-text("Preview")');
  478 |     if (await previewBtn.isVisible()) {
  479 |       await previewBtn.click();
  480 |
  481 |       // Should show quiz settings in preview
  482 |       const settingsInfo = page.locator('text=/80%/, text=/30/, text=/passing/, text=/time/');
  483 |       if (await settingsInfo.count() > 0) {
  484 |         await expect(settingsInfo.first()).toBeVisible();
  485 |       }
  486 |
  487 |       // Should show tags if displayed
  488 |       const tagsInfo = page.locator('text=preview, text=testing, text=settings');
  489 |       if (await tagsInfo.count() > 0) {
  490 |         await expect(tagsInfo.first()).toBeVisible();
  491 |       }
  492 |     }
  493 |   });
  494 |
  495 |   test('should preview quiz responsively', async ({ page }) => {
  496 |     // Create a quiz for responsive testing
  497 |     await page.goto('/dashboard/quizzes/create');
  498 |     await page.fill('input[id="title"]', 'Responsive Preview Quiz');
  499 |     await page.fill('textarea[id="description"]', 'Testing responsive preview');
  500 |     await page.click('button[type="submit"]');
  501 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  502 |
  503 |     // Add a question
  504 |     await page.click('button:has-text("Questions")');
  505 |     await page.click('button:has-text("Add Question")');
  506 |
  507 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  508 |     if (await questionTypeSelect.isVisible()) {
  509 |       await questionTypeSelect.selectOption('multiple_choice');
  510 |       await page.fill('textarea[id="questionText"]', 'Responsive test question');
  511 |
  512 |       const optionInputs = page.locator('input[placeholder*="option"]');
  513 |       if (await optionInputs.count() >= 2) {
  514 |         await optionInputs.nth(0).fill('Mobile Option');
  515 |         await optionInputs.nth(1).fill('Desktop Option');
  516 |       }
  517 |
  518 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  519 |       await page.waitForTimeout(1000);
  520 |     }
  521 |
  522 |     // Preview in different viewport sizes
  523 |     const viewports = [
  524 |       { width: 375, height: 667, name: 'Mobile' },
  525 |       { width: 768, height: 1024, name: 'Tablet' },
  526 |       { width: 1920, height: 1080, name: 'Desktop' }
  527 |     ];
  528 |
  529 |     for (const viewport of viewports) {
  530 |       await page.setViewportSize({ width: viewport.width, height: viewport.height });
  531 |
  532 |       const previewBtn = page.locator('button:has-text("Preview")');
  533 |       if (await previewBtn.isVisible()) {
> 534 |         await previewBtn.click();
      |                          ^ Error: locator.click: Test timeout of 60000ms exceeded.
  535 |
  536 |         // Should show content properly at this viewport
  537 |         await expect(page.locator('text=Responsive test question')).toBeVisible();
  538 |
  539 |         // Exit preview for next iteration
  540 |         const exitBtn = page.locator('button:has-text("Exit"), button:has-text("Close"), .exit-preview');
  541 |         if (await exitBtn.isVisible()) {
  542 |           await exitBtn.click();
  543 |         }
  544 |       }
  545 |     }
  546 |   });
  547 |
  548 |   test('should handle preview with no questions gracefully', async ({ page }) => {
  549 |     // Create quiz without questions
  550 |     await page.goto('/dashboard/quizzes/create');
  551 |     await page.fill('input[id="title"]', 'Empty Quiz Preview');
  552 |     await page.fill('textarea[id="description"]', 'Quiz with no questions');
  553 |     await page.click('button[type="submit"]');
  554 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  555 |
  556 |     // Try to preview
  557 |     const previewBtn = page.locator('button:has-text("Preview")');
  558 |     if (await previewBtn.isVisible()) {
  559 |       await previewBtn.click();
  560 |
  561 |       // Should show appropriate message
  562 |       const emptyMessage = page.locator('text=no questions, text=empty, text=add questions first');
  563 |       if (await emptyMessage.count() > 0) {
  564 |         await expect(emptyMessage.first()).toBeVisible();
  565 |       }
  566 |
  567 |       // Should still show quiz title and description
  568 |       await expect(page.locator('text=Empty Quiz Preview')).toBeVisible();
  569 |     }
  570 |   });
  571 | });
  572 |
```