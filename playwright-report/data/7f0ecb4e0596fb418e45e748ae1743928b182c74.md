# Test info

- Name: Mobile Tests >> should handle mobile quiz taking
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:207:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:213:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  113 |     await page.goto('/auth/login');
  114 |     await page.fill('input[id="email"]', '<EMAIL>');
  115 |     await page.fill('input[id="password"]', 'user123');
  116 |     await page.click('button[type="submit"]');
  117 |     await page.waitForURL('/dashboard');
  118 |     
  119 |     await page.goto('/security-quizzes');
  120 |     
  121 |     // Look for swipeable content (carousels, etc.)
  122 |     const swipeableContent = page.locator('.carousel, .swiper, .slider');
  123 |     if (await swipeableContent.isVisible()) {
  124 |       const contentBox = await swipeableContent.boundingBox();
  125 |       
  126 |       if (contentBox) {
  127 |         // Simulate swipe gesture
  128 |         await page.mouse.move(contentBox.x + contentBox.width * 0.8, contentBox.y + contentBox.height / 2);
  129 |         await page.mouse.down();
  130 |         await page.mouse.move(contentBox.x + contentBox.width * 0.2, contentBox.y + contentBox.height / 2);
  131 |         await page.mouse.up();
  132 |         
  133 |         // Content should respond to swipe
  134 |         await page.waitForTimeout(500);
  135 |         await expect(swipeableContent).toBeVisible();
  136 |       }
  137 |     }
  138 |   });
  139 |
  140 |   test('should optimize for different mobile sizes', async ({ page }) => {
  141 |     const mobileSizes = [
  142 |       { width: 320, height: 568, name: 'iPhone 5' },
  143 |       { width: 375, height: 667, name: 'iPhone SE' },
  144 |       { width: 414, height: 896, name: 'iPhone 11' },
  145 |       { width: 360, height: 640, name: 'Android' }
  146 |     ];
  147 |     
  148 |     for (const size of mobileSizes) {
  149 |       await page.setViewportSize({ width: size.width, height: size.height });
  150 |       await page.goto('/security-quizzes');
  151 |       
  152 |       // Should display content properly
  153 |       await expect(page.locator('h1')).toBeVisible();
  154 |       
  155 |       // Content should not overflow
  156 |       const body = page.locator('body');
  157 |       const bodyBox = await body.boundingBox();
  158 |       
  159 |       if (bodyBox) {
  160 |         expect(bodyBox.width).toBeLessThanOrEqual(size.width + 20); // Allow small margin
  161 |       }
  162 |     }
  163 |   });
  164 |
  165 |   test('should handle mobile form inputs', async ({ page }) => {
  166 |     await page.goto('/auth/register');
  167 |     
  168 |     // Test mobile keyboard types
  169 |     const emailInput = page.locator('input[type="email"], input[id="email"]');
  170 |     if (await emailInput.isVisible()) {
  171 |       const inputType = await emailInput.getAttribute('type');
  172 |       const inputMode = await emailInput.getAttribute('inputmode');
  173 |       
  174 |       // Should use appropriate input type for mobile keyboards
  175 |       expect(inputType === 'email' || inputMode === 'email').toBe(true);
  176 |     }
  177 |     
  178 |     // Test form validation on mobile
  179 |     await page.click('button[type="submit"]');
  180 |     
  181 |     // Should show validation messages
  182 |     const validationMessages = page.locator('.error-message, [aria-invalid="true"]');
  183 |     if (await validationMessages.count() > 0) {
  184 |       await expect(validationMessages.first()).toBeVisible();
  185 |     }
  186 |   });
  187 |
  188 |   test('should support mobile accessibility', async ({ page }) => {
  189 |     await page.goto('/auth/login');
  190 |     
  191 |     // Test touch target sizes
  192 |     const buttons = page.locator('button');
  193 |     const buttonCount = await buttons.count();
  194 |     
  195 |     for (let i = 0; i < Math.min(buttonCount, 5); i++) {
  196 |       const button = buttons.nth(i);
  197 |       const buttonBox = await button.boundingBox();
  198 |       
  199 |       if (buttonBox) {
  200 |         // Touch targets should be at least 44x44px
  201 |         expect(buttonBox.height).toBeGreaterThanOrEqual(40);
  202 |         expect(buttonBox.width).toBeGreaterThanOrEqual(40);
  203 |       }
  204 |     }
  205 |   });
  206 |
  207 |   test('should handle mobile quiz taking', async ({ page }) => {
  208 |     // Login and start a quiz
  209 |     await page.goto('/auth/login');
  210 |     await page.fill('input[id="email"]', '<EMAIL>');
  211 |     await page.fill('input[id="password"]', 'user123');
  212 |     await page.click('button[type="submit"]');
> 213 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  214 |     
  215 |     await page.goto('/security-quizzes');
  216 |     
  217 |     const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
  218 |     if (await firstQuiz.isVisible()) {
  219 |       await firstQuiz.click();
  220 |       
  221 |       const startBtn = page.locator('button:has-text("Start Quiz"), button:has-text("Begin")');
  222 |       if (await startBtn.isVisible()) {
  223 |         await startBtn.click();
  224 |         
  225 |         // Should show mobile-friendly quiz interface
  226 |         await expect(page.locator('.question-container, .question')).toBeVisible();
  227 |         
  228 |         // Answer options should be touch-friendly
  229 |         const options = page.locator('input[type="radio"], .option');
  230 |         if (await options.count() > 0) {
  231 |           const optionBox = await options.first().boundingBox();
  232 |           
  233 |           if (optionBox) {
  234 |             expect(optionBox.height).toBeGreaterThanOrEqual(40);
  235 |           }
  236 |         }
  237 |       }
  238 |     }
  239 |   });
  240 |
  241 |   test('should handle mobile orientation changes', async ({ page }) => {
  242 |     // Start in portrait
  243 |     await page.setViewportSize({ width: 375, height: 667 });
  244 |     await page.goto('/security-quizzes');
  245 |     
  246 |     await expect(page.locator('h1')).toBeVisible();
  247 |     
  248 |     // Switch to landscape
  249 |     await page.setViewportSize({ width: 667, height: 375 });
  250 |     await page.waitForTimeout(500);
  251 |     
  252 |     // Should still display content properly
  253 |     await expect(page.locator('h1')).toBeVisible();
  254 |     
  255 |     // Content should adapt to landscape
  256 |     const quizCards = page.locator('.quiz-card, .quiz-item');
  257 |     if (await quizCards.count() > 1) {
  258 |       const firstCard = quizCards.first();
  259 |       const secondCard = quizCards.nth(1);
  260 |       
  261 |       const firstBox = await firstCard.boundingBox();
  262 |       const secondBox = await secondCard.boundingBox();
  263 |       
  264 |       if (firstBox && secondBox) {
  265 |         // In landscape, cards might be side by side
  266 |         const isSideBySide = Math.abs(firstBox.y - secondBox.y) < 50;
  267 |         const isStacked = secondBox.y > firstBox.y + firstBox.height - 50;
  268 |         
  269 |         expect(isSideBySide || isStacked).toBe(true);
  270 |       }
  271 |     }
  272 |   });
  273 |
  274 |   test('should optimize mobile performance', async ({ page }) => {
  275 |     const startTime = Date.now();
  276 |     
  277 |     await page.goto('/security-quizzes');
  278 |     await page.waitForLoadState('networkidle');
  279 |     
  280 |     const loadTime = Date.now() - startTime;
  281 |     
  282 |     // Mobile should load within 5 seconds
  283 |     expect(loadTime).toBeLessThan(5000);
  284 |     
  285 |     // Should show content
  286 |     await expect(page.locator('.quiz-card, .quiz-item')).toHaveCount.greaterThan(0);
  287 |   });
  288 |
  289 |   test('should handle mobile search', async ({ page }) => {
  290 |     await page.goto('/auth/login');
  291 |     await page.fill('input[id="email"]', '<EMAIL>');
  292 |     await page.fill('input[id="password"]', 'user123');
  293 |     await page.click('button[type="submit"]');
  294 |     await page.waitForURL('/dashboard');
  295 |     
  296 |     await page.goto('/security-quizzes');
  297 |     
  298 |     // Look for search functionality
  299 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
  300 |     if (await searchInput.isVisible()) {
  301 |       // Should be properly sized for mobile
  302 |       const searchBox = await searchInput.boundingBox();
  303 |       
  304 |       if (searchBox) {
  305 |         expect(searchBox.height).toBeGreaterThanOrEqual(40);
  306 |       }
  307 |       
  308 |       // Test search functionality
  309 |       await searchInput.fill('security');
  310 |       await searchInput.press('Enter');
  311 |       
  312 |       await page.waitForTimeout(1000);
  313 |       
```