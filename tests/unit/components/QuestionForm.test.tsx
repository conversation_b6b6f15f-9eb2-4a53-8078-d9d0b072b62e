import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QuestionForm } from '@/components/quiz/editor/QuestionForm';

// Mock the question form components
jest.mock('@/components/quiz/editor/question-forms/MultipleChoiceForm', () => ({
  MultipleChoiceForm: ({ onSubmit }: { onSubmit: (data: any) => void }) => (
    <div data-testid="multiple-choice-form">
      <button onClick={() => onSubmit({ type: 'multiple_choice', text: 'Test question' })}>
        Submit MC
      </button>
    </div>
  ),
}));

jest.mock('@/components/quiz/editor/question-forms/TrueFalseForm', () => ({
  TrueFalseForm: ({ onSubmit }: { onSubmit: (data: any) => void }) => (
    <div data-testid="true-false-form">
      <button onClick={() => onSubmit({ type: 'true_false', text: 'Test TF question' })}>
        Submit TF
      </button>
    </div>
  ),
}));

describe('QuestionForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders question type selector', () => {
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByLabelText(/question type/i)).toBeInTheDocument();
  });

  it('shows multiple choice form when selected', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    await user.selectOptions(select, 'multiple_choice');

    expect(screen.getByTestId('multiple-choice-form')).toBeInTheDocument();
  });

  it('shows true/false form when selected', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    await user.selectOptions(select, 'true_false');

    expect(screen.getByTestId('true-false-form')).toBeInTheDocument();
  });

  it('calls onSubmit when form is submitted', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    await user.selectOptions(select, 'multiple_choice');

    const submitButton = screen.getByText('Submit MC');
    await user.click(submitButton);

    expect(mockOnSubmit).toHaveBeenCalledWith({
      type: 'multiple_choice',
      text: 'Test question'
    });
  });

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByText(/cancel/i);
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('pre-fills form when editing existing question', () => {
    const existingQuestion = {
      id: '1',
      type: 'multiple_choice',
      text: 'Existing question',
      points: 5,
    };

    render(
      <QuestionForm
        question={existingQuestion}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i) as HTMLSelectElement;
    expect(select.value).toBe('multiple_choice');
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Try to submit without selecting question type
    const form = screen.getByRole('form');
    fireEvent.submit(form);

    // Should not call onSubmit
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('handles form submission errors gracefully', async () => {
    const user = userEvent.setup();
    const mockOnSubmitWithError = jest.fn().mockRejectedValue(new Error('Submission failed'));
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmitWithError}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    await user.selectOptions(select, 'multiple_choice');

    const submitButton = screen.getByText('Submit MC');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmitWithError).toHaveBeenCalled();
    });

    // Should show error message
    expect(screen.getByText(/error/i)).toBeInTheDocument();
  });

  it('resets form when question type changes', async () => {
    const user = userEvent.setup();
    
    render(
      <QuestionForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const select = screen.getByLabelText(/question type/i);
    
    // Select multiple choice
    await user.selectOptions(select, 'multiple_choice');
    expect(screen.getByTestId('multiple-choice-form')).toBeInTheDocument();

    // Change to true/false
    await user.selectOptions(select, 'true_false');
    expect(screen.getByTestId('true-false-form')).toBeInTheDocument();
    expect(screen.queryByTestId('multiple-choice-form')).not.toBeInTheDocument();
  });
});
