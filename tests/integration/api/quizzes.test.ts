import { createMocks } from 'node-mocks-http';
import { GET, POST } from '@/app/api/quizzes/route';
import { GET as getQuiz, PUT as updateQuiz, DELETE as deleteQuiz } from '@/app/api/quizzes/[id]/route';
import { POST as addQuestion } from '@/app/api/quizzes/[id]/questions/route';

// Mock the database
jest.mock('@/lib/db', () => ({
  quiz: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  question: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
}));

// Mock NextAuth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

import { getServerSession } from 'next-auth';
import { db } from '@/lib/db';

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe('/api/quizzes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/quizzes', () => {
    it('returns quizzes for authenticated user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>', role: 'user' }
      } as any);

      const mockQuizzes = [
        {
          id: 'quiz1',
          title: 'Test Quiz',
          description: 'A test quiz',
          category: 'Web Security',
          difficulty: 'Beginner',
          createdBy: 'user1',
        },
      ];

      (db.quiz.findMany as jest.Mock).mockResolvedValue(mockQuizzes);

      const { req, res } = createMocks({ method: 'GET' });
      await GET(req as any);

      expect(db.quiz.findMany).toHaveBeenCalledWith({
        where: { createdBy: 'user1' },
        include: {
          questions: true,
          _count: { select: { questions: true } },
        },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('returns 401 for unauthenticated user', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const { req, res } = createMocks({ method: 'GET' });
      const response = await GET(req as any);

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/quizzes', () => {
    it('creates a new quiz for admin user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'admin1', email: '<EMAIL>', role: 'admin' }
      } as any);

      const newQuiz = {
        title: 'New Quiz',
        description: 'A new quiz',
        category: 'Network Security',
        difficulty: 'Intermediate',
      };

      const createdQuiz = { id: 'quiz2', ...newQuiz, createdBy: 'admin1' };
      (db.quiz.create as jest.Mock).mockResolvedValue(createdQuiz);

      const { req, res } = createMocks({
        method: 'POST',
        body: newQuiz,
      });

      const response = await POST(req as any);
      const responseData = await response.json();

      expect(response.status).toBe(201);
      expect(responseData).toEqual(createdQuiz);
      expect(db.quiz.create).toHaveBeenCalledWith({
        data: {
          ...newQuiz,
          createdBy: 'admin1',
        },
      });
    });

    it('returns 403 for non-admin user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>', role: 'user' }
      } as any);

      const { req, res } = createMocks({
        method: 'POST',
        body: { title: 'Test Quiz' },
      });

      const response = await POST(req as any);

      expect(response.status).toBe(403);
    });

    it('validates required fields', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'admin1', email: '<EMAIL>', role: 'admin' }
      } as any);

      const { req, res } = createMocks({
        method: 'POST',
        body: { description: 'Missing title' },
      });

      const response = await POST(req as any);

      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/quizzes/[id]', () => {
    it('returns quiz by id', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>', role: 'user' }
      } as any);

      const mockQuiz = {
        id: 'quiz1',
        title: 'Test Quiz',
        createdBy: 'user1',
        questions: [],
      };

      (db.quiz.findUnique as jest.Mock).mockResolvedValue(mockQuiz);

      const { req, res } = createMocks({ method: 'GET' });
      const response = await getQuiz(req as any, { params: Promise.resolve({ id: 'quiz1' }) });
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData).toEqual(mockQuiz);
    });

    it('returns 404 for non-existent quiz', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>', role: 'user' }
      } as any);

      (db.quiz.findUnique as jest.Mock).mockResolvedValue(null);

      const { req, res } = createMocks({ method: 'GET' });
      const response = await getQuiz(req as any, { params: Promise.resolve({ id: 'nonexistent' }) });

      expect(response.status).toBe(404);
    });
  });

  describe('POST /api/quizzes/[id]/questions', () => {
    it('adds question to quiz for admin user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'admin1', email: '<EMAIL>', role: 'admin' }
      } as any);

      const questionData = {
        type: 'multiple_choice',
        text: 'What is XSS?',
        points: 2,
        options: ['Cross-Site Scripting', 'Cross-Site Request Forgery'],
        correctAnswer: 0,
      };

      const createdQuestion = { id: 'question1', ...questionData, quizId: 'quiz1' };
      (db.question.create as jest.Mock).mockResolvedValue(createdQuestion);

      const { req, res } = createMocks({
        method: 'POST',
        body: questionData,
      });

      const response = await addQuestion(req as any, { params: Promise.resolve({ id: 'quiz1' }) });
      const responseData = await response.json();

      expect(response.status).toBe(201);
      expect(responseData).toEqual(createdQuestion);
    });

    it('handles multiple choice question data transformation', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'admin1', email: '<EMAIL>', role: 'admin' }
      } as any);

      const questionData = {
        type: 'multiple_choice',
        text: 'Test question',
        points: 2,
        options: ['Option 1', 'Option 2'],
        single_correct_answer: true,
        scoring_method: 'all_or_nothing',
      };

      const createdQuestion = { id: 'question1', quizId: 'quiz1' };
      (db.question.create as jest.Mock).mockResolvedValue(createdQuestion);

      const { req, res } = createMocks({
        method: 'POST',
        body: questionData,
      });

      await addQuestion(req as any, { params: Promise.resolve({ id: 'quiz1' }) });

      // Verify that single_correct_answer and scoring_method were moved to options
      const createCall = (db.question.create as jest.Mock).mock.calls[0][0];
      const savedData = createCall.data;
      
      expect(savedData.single_correct_answer).toBeUndefined();
      expect(savedData.scoring_method).toBeUndefined();
      
      const optionsData = JSON.parse(savedData.options);
      expect(optionsData.single_correct_answer).toBe(true);
      expect(optionsData.scoring_method).toBe('all_or_nothing');
    });

    it('returns 403 for non-admin user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>', role: 'user' }
      } as any);

      const { req, res } = createMocks({
        method: 'POST',
        body: { type: 'multiple_choice', text: 'Test' },
      });

      const response = await addQuestion(req as any, { params: Promise.resolve({ id: 'quiz1' }) });

      expect(response.status).toBe(403);
    });
  });
});
