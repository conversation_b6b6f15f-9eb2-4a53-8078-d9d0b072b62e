// Simple API utility tests
describe('API Utilities', () => {
  it('should validate quiz data structure', () => {
    const validQuiz = {
      title: 'Test Quiz',
      description: 'A test quiz',
      category: 'Web Security',
      difficulty: 'Beginner',
    };

    // Check required fields
    expect(validQuiz.title).toBeDefined();
    expect(validQuiz.description).toBeDefined();
    expect(validQuiz.category).toBeDefined();
    expect(validQuiz.difficulty).toBeDefined();
  });

  it('should validate question data structure', () => {
    const validQuestion = {
      type: 'multiple_choice',
      text: 'What is XSS?',
      points: 2,
      options: ['Cross-Site Scripting', 'SQL Injection'],
      correctAnswer: 0,
    };

    // Check required fields
    expect(validQuestion.type).toBeDefined();
    expect(validQuestion.text).toBeDefined();
    expect(validQuestion.points).toBeGreaterThan(0);
    expect(Array.isArray(validQuestion.options)).toBe(true);
  });

  it('should handle different question types', () => {
    const questionTypes = [
      'multiple_choice',
      'true_false',
      'short_answer',
      'matching',
      'fill_in_the_blank',
      'essay'
    ];

    questionTypes.forEach(type => {
      expect(typeof type).toBe('string');
      expect(type.length).toBeGreaterThan(0);
    });
  });

  it('should validate difficulty levels', () => {
    const difficulties = ['Beginner', 'Intermediate', 'Advanced'];

    difficulties.forEach(difficulty => {
      expect(typeof difficulty).toBe('string');
      expect(['Beginner', 'Intermediate', 'Advanced']).toContain(difficulty);
    });
  });

  it('should validate categories', () => {
    const categories = [
      'Web Application Security',
      'Network Security',
      'Cryptography',
      'Incident Response',
      'Penetration Testing'
    ];

    categories.forEach(category => {
      expect(typeof category).toBe('string');
      expect(category.length).toBeGreaterThan(0);
    });
  });
});

describe('Data Processing', () => {
  it('should process multiple choice question data correctly', () => {
    const questionData = {
      type: 'multiple_choice',
      text: 'Test question',
      points: 2,
      options: ['Option 1', 'Option 2'],
      single_correct_answer: true,
      scoring_method: 'all_or_nothing',
    };

    // Simulate the data transformation that happens in the API
    const { single_correct_answer, scoring_method, ...cleanData } = questionData;

    // Verify the extracted values
    expect(single_correct_answer).toBe(true);
    expect(scoring_method).toBe('all_or_nothing');

    // Verify clean data doesn't have these fields
    expect('single_correct_answer' in cleanData).toBe(false);
    expect('scoring_method' in cleanData).toBe(false);
    expect(cleanData.options).toBeDefined();
    expect(cleanData.type).toBe('multiple_choice');
  });

  it('should handle JSON stringification correctly', () => {
    const data = {
      options: ['Option 1', 'Option 2'],
      correctAnswer: 0,
    };

    const stringifiedOptions = JSON.stringify(data.options);
    const stringifiedAnswer = JSON.stringify(data.correctAnswer);

    expect(typeof stringifiedOptions).toBe('string');
    expect(typeof stringifiedAnswer).toBe('string');

    // Should be able to parse back
    expect(JSON.parse(stringifiedOptions)).toEqual(data.options);
    expect(JSON.parse(stringifiedAnswer)).toEqual(data.correctAnswer);
  });

  it('should generate unique question IDs', () => {
    const generateQuestionId = () => `question_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const id1 = generateQuestionId();
    const id2 = generateQuestionId();

    expect(id1).not.toBe(id2);
    expect(id1).toMatch(/^question_\d+_[a-z0-9]+$/);
    expect(id2).toMatch(/^question_\d+_[a-z0-9]+$/);
  });
});
