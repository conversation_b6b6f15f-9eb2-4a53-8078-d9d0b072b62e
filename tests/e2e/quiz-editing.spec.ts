import { test, expect } from '@playwright/test';

test.describe('Quiz Editing - All Possibilities', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should edit basic quiz information', async ({ page }) => {
    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Original Quiz Title');
    await page.fill('textarea[id="description"]', 'Original description');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Edit the quiz information
    await page.fill('input[id="title"]', 'Updated Quiz Title');
    await page.fill('textarea[id="description"]', 'Updated description with more details');

    // Save changes (auto-save or manual save)
    const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
    if (await saveBtn.isVisible()) {
      await saveBtn.click();
      await page.waitForTimeout(1000);
    }

    // Refresh page to verify changes were saved
    await page.reload();
    await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
    await expect(page.locator('textarea[id="description"]')).toHaveValue('Updated description with more details');
  });

  test('should edit quiz settings', async ({ page }) => {
    // Create a quiz with initial settings
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Settings Edit Test');
    await page.fill('textarea[id="description"]', 'Testing settings editing');
    await page.fill('input[id="passingScore"]', '70');
    await page.fill('input[id="timeLimit"]', '20');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Edit the settings
    await page.fill('input[id="passingScore"]', '85');
    await page.fill('input[id="timeLimit"]', '45');
    await page.fill('input[id="tags"]', 'updated, settings, test');

    // Save changes
    const saveBtn = page.locator('button:has-text("Save")');
    if (await saveBtn.isVisible()) {
      await saveBtn.click();
      await page.waitForTimeout(1000);
    }

    // Verify changes
    await page.reload();
    await expect(page.locator('input[id="passingScore"]')).toHaveValue('85');
    await expect(page.locator('input[id="timeLimit"]')).toHaveValue('45');
    await expect(page.locator('input[id="tags"]')).toHaveValue('updated, settings, test');
  });

  test('should add questions to existing quiz', async ({ page }) => {
    // Create a quiz without questions
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Addition Test');
    await page.fill('textarea[id="description"]', 'Testing adding questions');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Navigate to questions section
    await page.click('button:has-text("Questions"), .questions-tab');

    // Add first question
    await page.click('button:has-text("Add Question")');

    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'What is SQL injection?');
      await page.fill('input[id="points"]', '3');

      // Add options
      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 4) {
        await optionInputs.nth(0).fill('A database query attack');
        await optionInputs.nth(1).fill('A network protocol');
        await optionInputs.nth(2).fill('A programming language');
        await optionInputs.nth(3).fill('An encryption method');
      }

      // Mark correct answer
      const correctAnswerRadio = page.locator('input[type="radio"][name="correctOption"]');
      if (await correctAnswerRadio.count() > 0) {
        await correctAnswerRadio.first().check();
      }

      await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
      await page.waitForTimeout(1000);
    }

    // Add second question of different type
    await page.click('button:has-text("Add Question")');

    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('true_false');
      await page.fill('textarea[id="questionText"]', 'HTTPS is always secure.');

      const trueFalseOptions = page.locator('input[value="false"]');
      if (await trueFalseOptions.count() > 0) {
        await trueFalseOptions.first().check();
      }

      await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
      await page.waitForTimeout(1000);
    }

    // Verify questions were added
    await page.click('button:has-text("Existing Questions"), .existing-questions');
    await expect(page.locator('text=What is SQL injection?')).toBeVisible();
    await expect(page.locator('text=HTTPS is always secure')).toBeVisible();
  });

  test('should edit existing questions', async ({ page }) => {
    // Create quiz with a question first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Edit Test');
    await page.fill('textarea[id="description"]', 'Testing question editing');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Add initial question
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');

    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'Original question text');

      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('Original Option A');
        await optionInputs.nth(1).fill('Original Option B');
      }

      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
    }

    // Now edit the question
    await page.click('button:has-text("Existing Questions")');

    const editBtn = page.locator('button:has-text("Edit"), .edit-question, .question-edit');
    if (await editBtn.isVisible()) {
      await editBtn.first().click();

      // Edit question text
      await page.fill('textarea[id="questionText"]', 'Updated question text with more detail');

      // Edit options
      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('Updated Option A');
        await optionInputs.nth(1).fill('Updated Option B');
      }

      // Save changes
      await page.click('button:has-text("Save"), button:has-text("Update")');
      await page.waitForTimeout(1000);

      // Verify changes
      await expect(page.locator('text=Updated question text')).toBeVisible();
      await expect(page.locator('text=Updated Option A')).toBeVisible();
    }
  });

  test('should delete questions from quiz', async ({ page }) => {
    // Create quiz with multiple questions
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Deletion Test');
    await page.fill('textarea[id="description"]', 'Testing question deletion');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Add two questions
    await page.click('button:has-text("Questions")');

    for (let i = 1; i <= 2; i++) {
      await page.click('button:has-text("Add Question")');

      const questionTypeSelect = page.locator('select[id="questionType"]');
      if (await questionTypeSelect.isVisible()) {
        await questionTypeSelect.selectOption('multiple_choice');
        await page.fill('textarea[id="questionText"]', `Question ${i} to be deleted`);

        const optionInputs = page.locator('input[placeholder*="option"]');
        if (await optionInputs.count() >= 2) {
          await optionInputs.nth(0).fill(`Option A${i}`);
          await optionInputs.nth(1).fill(`Option B${i}`);
        }

        await page.click('button:has-text("Add Question"), button:has-text("Save")');
        await page.waitForTimeout(1000);
      }
    }

    // Delete first question
    await page.click('button:has-text("Existing Questions")');

    const deleteBtn = page.locator('button:has-text("Delete"), .delete-question, .question-delete');
    if (await deleteBtn.isVisible()) {
      await deleteBtn.first().click();

      // Confirm deletion if confirmation dialog appears
      const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes"), button:has-text("Delete")');
      if (await confirmBtn.isVisible()) {
        await confirmBtn.click();
        await page.waitForTimeout(1000);
      }

      // Verify question was deleted
      await expect(page.locator('text=Question 1 to be deleted')).not.toBeVisible();
      await expect(page.locator('text=Question 2 to be deleted')).toBeVisible();
    }
  });

  test('should reorder questions in quiz', async ({ page }) => {
    // Create quiz with multiple questions
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Reorder Test');
    await page.fill('textarea[id="description"]', 'Testing question reordering');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Add three questions
    await page.click('button:has-text("Questions")');

    for (let i = 1; i <= 3; i++) {
      await page.click('button:has-text("Add Question")');

      const questionTypeSelect = page.locator('select[id="questionType"]');
      if (await questionTypeSelect.isVisible()) {
        await questionTypeSelect.selectOption('multiple_choice');
        await page.fill('textarea[id="questionText"]', `Question ${i} for reordering`);

        const optionInputs = page.locator('input[placeholder*="option"]');
        if (await optionInputs.count() >= 2) {
          await optionInputs.nth(0).fill(`Q${i} Option A`);
          await optionInputs.nth(1).fill(`Q${i} Option B`);
        }

        await page.click('button:has-text("Add Question"), button:has-text("Save")');
        await page.waitForTimeout(1000);
      }
    }

    // Test reordering (look for drag handles or move up/down buttons)
    await page.click('button:has-text("Existing Questions")');

    const moveUpBtn = page.locator('button:has-text("Move Up"), .move-up, .question-up');
    const moveDownBtn = page.locator('button:has-text("Move Down"), .move-down, .question-down');

    if (await moveUpBtn.count() > 0 || await moveDownBtn.count() > 0) {
      // Try to move second question up
      if (await moveUpBtn.count() >= 2) {
        await moveUpBtn.nth(1).click();
        await page.waitForTimeout(1000);
      }

      // Verify order changed (this is a basic check)
      const questionTexts = await page.locator('.question-text, .question-title').allTextContents();
      expect(questionTexts.length).toBeGreaterThan(0);
    }
  });

  test('should edit quiz with different question types', async ({ page }) => {
    // Create quiz and add various question types
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Multi-Type Edit Test');
    await page.fill('textarea[id="description"]', 'Testing editing with different question types');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    await page.click('button:has-text("Questions")');

    const questionTypes = ['multiple_choice', 'true_false', 'short_answer'];

    for (let i = 0; i < questionTypes.length; i++) {
      await page.click('button:has-text("Add Question")');

      const questionTypeSelect = page.locator('select[id="questionType"]');
      if (await questionTypeSelect.isVisible()) {
        await questionTypeSelect.selectOption(questionTypes[i]);
        await page.fill('textarea[id="questionText"]', `${questionTypes[i]} question for editing`);

        if (questionTypes[i] === 'multiple_choice') {
          const optionInputs = page.locator('input[placeholder*="option"]');
          if (await optionInputs.count() >= 2) {
            await optionInputs.nth(0).fill('MC Option A');
            await optionInputs.nth(1).fill('MC Option B');
          }
        } else if (questionTypes[i] === 'true_false') {
          const trueFalseOptions = page.locator('input[value="true"]');
          if (await trueFalseOptions.count() > 0) {
            await trueFalseOptions.first().check();
          }
        } else if (questionTypes[i] === 'short_answer') {
          const answerInput = page.locator('input[placeholder*="correct answer"]').first();
          if (await answerInput.isVisible()) {
            await answerInput.fill('Sample correct answer');
          }
        }

        await page.click('button:has-text("Add Question"), button:has-text("Save")');
        await page.waitForTimeout(1000);
      }
    }

    // Verify all question types were added
    await page.click('button:has-text("Existing Questions")');
    await expect(page.locator('text=multiple_choice question')).toBeVisible();
    await expect(page.locator('text=true_false question')).toBeVisible();
    await expect(page.locator('text=short_answer question')).toBeVisible();
  });

  test('should handle quiz publishing and unpublishing', async ({ page }) => {
    // Create a complete quiz
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Publishing Test Quiz');
    await page.fill('textarea[id="description"]', 'Testing quiz publishing functionality');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Add at least one question
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');

    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'Ready to publish question');

      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('Publish Option A');
        await optionInputs.nth(1).fill('Publish Option B');
      }

      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
    }

    // Test publishing
    const publishBtn = page.locator('button:has-text("Publish"), .publish-quiz');
    if (await publishBtn.isVisible()) {
      await publishBtn.click();

      // Confirm publishing if confirmation dialog appears
      const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
      if (await confirmBtn.isVisible()) {
        await confirmBtn.click();
        await page.waitForTimeout(1000);
      }

      // Should show published status
      await expect(page.locator('text=Published, .published-status')).toBeVisible();

      // Test unpublishing
      const unpublishBtn = page.locator('button:has-text("Unpublish"), .unpublish-quiz');
      if (await unpublishBtn.isVisible()) {
        await unpublishBtn.click();

        const confirmUnpublish = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
        if (await confirmUnpublish.isVisible()) {
          await confirmUnpublish.click();
          await page.waitForTimeout(1000);
        }

        // Should show draft status
        await expect(page.locator('text=Draft, .draft-status')).toBeVisible();
      }
    }
  });

  test('should validate quiz before saving', async ({ page }) => {
    // Create quiz and test validation
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Validation Test Quiz');
    await page.fill('textarea[id="description"]', 'Testing validation');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Try to clear required fields and save
    await page.fill('input[id="title"]', '');

    const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
    if (await saveBtn.isVisible()) {
      await saveBtn.click();

      // Should show validation error
      const titleInput = page.locator('input[id="title"]');
      const isValid = await titleInput.evaluate(el => (el as HTMLInputElement).validity.valid);
      expect(isValid).toBe(false);
    }

    // Test invalid time limit
    await page.fill('input[id="title"]', 'Valid Title');
    await page.fill('input[id="timeLimit"]', '-5');

    if (await saveBtn.isVisible()) {
      await saveBtn.click();

      // Should show validation error for negative time
      const timeLimitInput = page.locator('input[id="timeLimit"]');
      const timeValid = await timeLimitInput.evaluate(el => (el as HTMLInputElement).validity.valid);
      expect(timeValid).toBe(false);
    }
  });
});
