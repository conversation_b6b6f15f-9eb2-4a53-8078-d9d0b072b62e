import { test, expect } from '@playwright/test';

test.describe('Quiz Editor', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard');
  });

  test('should create a new quiz', async ({ page }) => {
    // Navigate to create quiz page
    await page.goto('/dashboard/quizzes/create');

    // Fill in quiz details
    await page.fill('input[id="title"]', 'Test Quiz');
    await page.fill('textarea[id="description"]', 'This is a test quiz');
    await page.fill('input[id="tags"]', 'security, testing');
    await page.fill('input[id="passingScore"]', '80');
    await page.fill('input[id="timeLimit"]', '20');

    // Save quiz
    await page.click('button:has-text("Create Quiz")');

    // Should redirect to quiz editor
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Verify quiz was created
    await expect(page.locator('h1')).toContainText('Edit Quiz');
  });

  test('should add a multiple choice question', async ({ page }) => {
    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Test Quiz for Questions');
    await page.fill('textarea[id="description"]', 'Testing question creation');
    await page.click('button:has-text("Create Quiz")');

    // Wait for redirect to editor
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Navigate to Questions tab
    await page.click('button:has-text("Questions")');

    // Click Add Question tab
    await page.click('button:has-text("Add Question")');

    // Select Multiple Choice question type
    await page.selectOption('select[id="questionType"]', 'multiple_choice');

    // Fill in question details
    await page.fill('textarea[id="questionText"]', 'What is the most common web vulnerability?');
    await page.fill('input[id="points"]', '2');

    // Fill in options
    const optionInputs = page.locator('input[placeholder="Enter option text"]');
    await optionInputs.nth(0).fill('SQL Injection');
    await optionInputs.nth(1).fill('Cross-Site Scripting (XSS)');

    // Mark first option as correct
    await page.click('input[type="radio"][name="correctOption"]');

    // Add feedback
    await page.fill('textarea[id="feedbackCorrect"]', 'Correct! SQL Injection is indeed very common.');
    await page.fill('textarea[id="feedbackIncorrect"]', 'Not quite right. Try again.');

    // Save the question
    await page.click('button:has-text("Add Question")');

    // Verify question was added
    await page.waitForSelector('text=Question added successfully', { timeout: 10000 });

    // Check if question appears in the list
    await page.click('button:has-text("Existing Questions")');
    await expect(page.locator('text=What is the most common web vulnerability?')).toBeVisible();
  });

  test('should edit an existing question', async ({ page }) => {
    // Navigate to a quiz with existing questions
    await page.goto('/dashboard/quizzes');

    // Click on the first quiz
    await page.click('.quiz-card:first-child a');

    // Navigate to Questions tab
    await page.click('button:has-text("Questions")');

    // Click on first question to edit
    await page.click('.question-item:first-child');

    // Modify question text
    const questionTextarea = page.locator('textarea[id="questionText"]');
    await questionTextarea.clear();
    await questionTextarea.fill('Updated question text');

    // Save changes
    await page.click('button:has-text("Update Question")');

    // Verify update was successful
    await page.waitForSelector('text=Question updated successfully', { timeout: 10000 });
  });

  test('should delete a question', async ({ page }) => {
    // Navigate to a quiz with existing questions
    await page.goto('/dashboard/quizzes');

    // Click on the first quiz
    await page.click('.quiz-card:first-child a');

    // Navigate to Questions tab
    await page.click('button:has-text("Questions")');

    // Get initial question count
    const initialQuestions = await page.locator('.question-item').count();

    // Click delete button on first question
    await page.click('.question-item:first-child button[aria-label="Delete"]');

    // Confirm deletion in dialog
    page.on('dialog', dialog => dialog.accept());

    // Verify question was deleted
    await page.waitForFunction(
      (expectedCount) => document.querySelectorAll('.question-item').length === expectedCount,
      initialQuestions - 1
    );
  });

  test('should validate required fields', async ({ page }) => {
    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Validation Test Quiz');
    await page.fill('textarea[id="description"]', 'Testing validation');
    await page.click('button:has-text("Create Quiz")');

    // Wait for redirect to editor
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    // Navigate to Questions tab
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');

    // Try to save without filling required fields
    await page.click('button:has-text("Add Question")');

    // Should show validation errors
    await expect(page.locator('textarea[id="questionText"]:invalid')).toBeVisible();
  });

  test('should preview quiz', async ({ page }) => {
    // Navigate to a quiz
    await page.goto('/dashboard/quizzes');
    await page.click('.quiz-card:first-child a');

    // Click preview button
    await page.click('button:has-text("Preview Quiz")');

    // Should open preview in new tab/window
    const [previewPage] = await Promise.all([
      page.waitForEvent('popup'),
      page.click('button:has-text("Preview Quiz")')
    ]);

    // Verify preview page loaded
    await expect(previewPage.locator('h1')).toBeVisible();
  });

  test('should handle different question types', async ({ page }) => {
    // Create a quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Question Types Test');
    await page.fill('textarea[id="description"]', 'Testing different question types');
    await page.click('button:has-text("Create Quiz")');

    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');

    // Test True/False question
    await page.selectOption('select[id="questionType"]', 'true_false');
    await page.fill('textarea[id="questionText"]', 'Is this a true/false question?');
    await page.click('input[value="true"]');
    await page.click('button:has-text("Add Question")');

    await page.waitForSelector('text=Question added successfully');

    // Test Short Answer question
    await page.selectOption('select[id="questionType"]', 'short_answer');
    await page.fill('textarea[id="questionText"]', 'What is your name?');
    await page.fill('input[placeholder="Enter correct answer"]', 'John Doe');
    await page.click('button:has-text("Add Question")');

    await page.waitForSelector('text=Question added successfully');
  });

  test('should save quiz progress automatically', async ({ page }) => {
    // Navigate to a quiz with questions
    await page.goto('/dashboard/quizzes');
    await page.click('.quiz-card:first-child a');

    // Make changes to quiz metadata
    await page.fill('input[id="title"]', 'Updated Quiz Title');

    // Wait for auto-save indicator
    await page.waitForSelector('text=Saved', { timeout: 5000 });

    // Refresh page to verify changes were saved
    await page.reload();
    await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
  });

  test('should handle quiz publishing', async ({ page }) => {
    // Navigate to a quiz
    await page.goto('/dashboard/quizzes');
    await page.click('.quiz-card:first-child a');

    // Publish the quiz
    await page.click('button:has-text("Publish Quiz")');

    // Confirm publishing
    await page.click('button:has-text("Confirm")');

    // Should show published status
    await expect(page.locator('text=Published')).toBeVisible();
  });
});
