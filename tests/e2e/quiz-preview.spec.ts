import { test, expect } from '@playwright/test';

test.describe('Quiz Preview Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should preview empty quiz', async ({ page }) => {
    // Create a basic quiz first
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Preview Test Quiz');
    await page.fill('textarea[id="description"]', 'A quiz for testing preview functionality');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Look for preview button
    const previewBtn = page.locator('button:has-text("Preview"), a:has-text("Preview"), .preview-button');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show preview interface
      await expect(page.locator('.preview-mode, .quiz-preview')).toBeVisible();
      
      // Should show quiz title and description
      await expect(page.locator('h1, h2')).toContainText('Preview Test Quiz');
      await expect(page.locator('text=A quiz for testing preview functionality')).toBeVisible();
      
      // Should show message about no questions
      await expect(page.locator('text=no questions, text=empty, text=add questions')).toBeVisible();
    }
  });

  test('should preview quiz with single question', async ({ page }) => {
    // Create quiz with one question
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Single Question Preview');
    await page.fill('textarea[id="description"]', 'Quiz with one question for preview testing');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Add a question
    await page.click('button:has-text("Questions"), .questions-tab');
    await page.click('button:has-text("Add Question"), .add-question');
    
    // Fill question details
    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
    }
    
    await page.fill('textarea[id="questionText"]', 'What is the most common web vulnerability?');
    await page.fill('input[id="points"]', '2');
    
    // Add options
    const optionInputs = page.locator('input[placeholder*="option"], .option-input');
    if (await optionInputs.count() > 0) {
      await optionInputs.nth(0).fill('SQL Injection');
      await optionInputs.nth(1).fill('Cross-Site Scripting (XSS)');
      await optionInputs.nth(2).fill('Cross-Site Request Forgery (CSRF)');
      await optionInputs.nth(3).fill('Buffer Overflow');
    }
    
    // Mark correct answer
    const correctAnswerRadio = page.locator('input[type="radio"][name="correctOption"]');
    if (await correctAnswerRadio.count() > 0) {
      await correctAnswerRadio.first().check();
    }
    
    await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
    await page.waitForTimeout(1000);
    
    // Now preview the quiz
    const previewBtn = page.locator('button:has-text("Preview"), a:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show quiz with question
      await expect(page.locator('text=What is the most common web vulnerability?')).toBeVisible();
      await expect(page.locator('text=SQL Injection')).toBeVisible();
      await expect(page.locator('text=Cross-Site Scripting')).toBeVisible();
    }
  });

  test('should preview quiz with multiple question types', async ({ page }) => {
    // Create quiz with multiple question types
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Multi-Type Question Preview');
    await page.fill('textarea[id="description"]', 'Quiz with different question types');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Add multiple choice question
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');
    
    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'Which protocol is used for secure web communication?');
      
      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('HTTP');
        await optionInputs.nth(1).fill('HTTPS');
      }
      
      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
      
      // Add true/false question
      await page.click('button:has-text("Add Question")');
      await questionTypeSelect.selectOption('true_false');
      await page.fill('textarea[id="questionText"]', 'HTTPS encrypts all data transmission.');
      
      const trueFalseOptions = page.locator('input[value="true"], input[value="false"]');
      if (await trueFalseOptions.count() > 0) {
        await trueFalseOptions.first().check();
      }
      
      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
    }
    
    // Preview the quiz
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show both questions
      await expect(page.locator('text=Which protocol is used for secure web communication?')).toBeVisible();
      await expect(page.locator('text=HTTPS encrypts all data transmission')).toBeVisible();
    }
  });

  test('should preview quiz navigation', async ({ page }) => {
    // Create quiz with multiple questions for navigation testing
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Navigation Preview Test');
    await page.fill('textarea[id="description"]', 'Testing navigation in preview mode');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Add two questions quickly
    await page.click('button:has-text("Questions")');
    
    for (let i = 1; i <= 2; i++) {
      await page.click('button:has-text("Add Question")');
      
      const questionTypeSelect = page.locator('select[id="questionType"]');
      if (await questionTypeSelect.isVisible()) {
        await questionTypeSelect.selectOption('multiple_choice');
        await page.fill('textarea[id="questionText"]', `Question ${i}: Test question for navigation`);
        
        const optionInputs = page.locator('input[placeholder*="option"]');
        if (await optionInputs.count() >= 2) {
          await optionInputs.nth(0).fill(`Option A for Q${i}`);
          await optionInputs.nth(1).fill(`Option B for Q${i}`);
        }
        
        await page.click('button:has-text("Add Question"), button:has-text("Save")');
        await page.waitForTimeout(1000);
      }
    }
    
    // Preview the quiz
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show first question
      await expect(page.locator('text=Question 1')).toBeVisible();
      
      // Test navigation
      const nextBtn = page.locator('button:has-text("Next"), .next-question');
      if (await nextBtn.isVisible()) {
        await nextBtn.click();
        await expect(page.locator('text=Question 2')).toBeVisible();
        
        // Test previous navigation
        const prevBtn = page.locator('button:has-text("Previous"), .prev-question');
        if (await prevBtn.isVisible()) {
          await prevBtn.click();
          await expect(page.locator('text=Question 1')).toBeVisible();
        }
      }
    }
  });

  test('should preview quiz with time limit', async ({ page }) => {
    // Create quiz with time limit
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Timed Preview Quiz');
    await page.fill('textarea[id="description"]', 'Quiz with time limit for preview');
    await page.fill('input[id="timeLimit"]', '10');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Add a question
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');
    
    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'Timed question for preview');
      
      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('Option A');
        await optionInputs.nth(1).fill('Option B');
      }
      
      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
    }
    
    // Preview the quiz
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show timer or time limit information
      const timerElements = page.locator('.timer, .countdown, text=/time/i, text=/10/');
      if (await timerElements.count() > 0) {
        await expect(timerElements.first()).toBeVisible();
      }
    }
  });

  test('should exit preview mode', async ({ page }) => {
    // Create and preview a quiz
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Exit Preview Test');
    await page.fill('textarea[id="description"]', 'Testing exit from preview mode');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Enter preview mode
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Look for exit/close preview button
      const exitBtn = page.locator('button:has-text("Exit"), button:has-text("Close"), button:has-text("Back to Edit"), .exit-preview');
      if (await exitBtn.isVisible()) {
        await exitBtn.click();
        
        // Should return to edit mode
        await expect(page.locator('input[id="title"]')).toBeVisible();
        await expect(page.locator('input[id="title"]')).toHaveValue('Exit Preview Test');
      }
    }
  });

  test('should preview quiz with different settings', async ({ page }) => {
    // Create quiz with various settings
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Settings Preview Quiz');
    await page.fill('textarea[id="description"]', 'Quiz with various settings for preview');
    await page.fill('input[id="passingScore"]', '80');
    await page.fill('input[id="timeLimit"]', '30');
    await page.fill('input[id="tags"]', 'preview, testing, settings');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Preview the quiz
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show quiz settings in preview
      const settingsInfo = page.locator('text=/80%/, text=/30/, text=/passing/, text=/time/');
      if (await settingsInfo.count() > 0) {
        await expect(settingsInfo.first()).toBeVisible();
      }
      
      // Should show tags if displayed
      const tagsInfo = page.locator('text=preview, text=testing, text=settings');
      if (await tagsInfo.count() > 0) {
        await expect(tagsInfo.first()).toBeVisible();
      }
    }
  });

  test('should preview quiz responsively', async ({ page }) => {
    // Create a quiz for responsive testing
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Responsive Preview Quiz');
    await page.fill('textarea[id="description"]', 'Testing responsive preview');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Add a question
    await page.click('button:has-text("Questions")');
    await page.click('button:has-text("Add Question")');
    
    const questionTypeSelect = page.locator('select[id="questionType"]');
    if (await questionTypeSelect.isVisible()) {
      await questionTypeSelect.selectOption('multiple_choice');
      await page.fill('textarea[id="questionText"]', 'Responsive test question');
      
      const optionInputs = page.locator('input[placeholder*="option"]');
      if (await optionInputs.count() >= 2) {
        await optionInputs.nth(0).fill('Mobile Option');
        await optionInputs.nth(1).fill('Desktop Option');
      }
      
      await page.click('button:has-text("Add Question"), button:has-text("Save")');
      await page.waitForTimeout(1000);
    }
    
    // Preview in different viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      const previewBtn = page.locator('button:has-text("Preview")');
      if (await previewBtn.isVisible()) {
        await previewBtn.click();
        
        // Should show content properly at this viewport
        await expect(page.locator('text=Responsive test question')).toBeVisible();
        
        // Exit preview for next iteration
        const exitBtn = page.locator('button:has-text("Exit"), button:has-text("Close"), .exit-preview');
        if (await exitBtn.isVisible()) {
          await exitBtn.click();
        }
      }
    }
  });

  test('should handle preview with no questions gracefully', async ({ page }) => {
    // Create quiz without questions
    await page.goto('/dashboard/quizzes/create');
    await page.fill('input[id="title"]', 'Empty Quiz Preview');
    await page.fill('textarea[id="description"]', 'Quiz with no questions');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
    
    // Try to preview
    const previewBtn = page.locator('button:has-text("Preview")');
    if (await previewBtn.isVisible()) {
      await previewBtn.click();
      
      // Should show appropriate message
      const emptyMessage = page.locator('text=no questions, text=empty, text=add questions first');
      if (await emptyMessage.count() > 0) {
        await expect(emptyMessage.first()).toBeVisible();
      }
      
      // Should still show quiz title and description
      await expect(page.locator('text=Empty Quiz Preview')).toBeVisible();
    }
  });
});
