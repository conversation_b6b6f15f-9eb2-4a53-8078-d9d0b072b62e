import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
  });

  test('should load dashboard quickly', async ({ page }) => {
    const startTime = Date.now();

    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');

    const loadTime = Date.now() - startTime;

    // Dashboard should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);

    // Should show main content
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should load quiz list efficiently', async ({ page }) => {
    const startTime = Date.now();

    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');

    const loadTime = Date.now() - startTime;

    // Quiz list should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);

    // Should show quiz cards
    const quizCards = page.locator('.quiz-card, .quiz-item');
    const cardCount = await quizCards.count();
    expect(cardCount).toBeGreaterThan(0);
  });

  test('should handle large quiz efficiently', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Find and start a quiz
    const quizCard = page.locator('.quiz-card, .quiz-item').first();
    await quizCard.click();

    const startTime = Date.now();

    // Start quiz
    const startBtn = page.locator('button:has-text("Start Quiz"), button:has-text("Begin")');
    if (await startBtn.isVisible()) {
      await startBtn.click();
      await page.waitForLoadState('networkidle');

      const loadTime = Date.now() - startTime;

      // Quiz should start within 3 seconds
      expect(loadTime).toBeLessThan(3000);

      // Should show first question
      await expect(page.locator('.question-container, .question')).toBeVisible();
    }
  });

  test('should search quickly', async ({ page }) => {
    await page.goto('/security-quizzes');

    const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
    if (await searchInput.isVisible()) {
      const startTime = Date.now();

      await searchInput.fill('security');
      await searchInput.press('Enter');

      // Wait for search results
      await page.waitForTimeout(100); // Small delay to start search
      await page.waitForLoadState('networkidle');

      const searchTime = Date.now() - startTime;

      // Search should complete within 2 seconds
      expect(searchTime).toBeLessThan(2000);

      // Should show results
      const results = page.locator('.quiz-card, .quiz-item, .no-results');
      await expect(results).toHaveCount.greaterThan(0);
    }
  });

  test('should handle concurrent users simulation', async ({ browser }) => {
    // Create multiple browser contexts to simulate concurrent users
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);

    const pages = await Promise.all(contexts.map(context => context.newPage()));

    const startTime = Date.now();

    // Simulate multiple users accessing the site simultaneously
    await Promise.all(pages.map(async (page, index) => {
      await page.goto('/auth/login');
      await page.fill('input[id="email"]', `user${index}@quizflow.com`);
      await page.fill('input[id="password"]', 'user123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');
      await page.goto('/security-quizzes');
    }));

    const totalTime = Date.now() - startTime;

    // All users should be able to access within 10 seconds
    expect(totalTime).toBeLessThan(10000);

    // Verify all pages loaded correctly
    for (const page of pages) {
      const quizCards = page.locator('.quiz-card, .quiz-item');
      const cardCount = await quizCards.count();
      expect(cardCount).toBeGreaterThan(0);
    }

    // Cleanup
    await Promise.all(contexts.map(context => context.close()));
  });

  test('should handle memory usage efficiently', async ({ page }) => {
    // Navigate through multiple pages to test memory usage
    const pages = [
      '/dashboard',
      '/security-quizzes',
      '/dashboard/analytics',
      '/dashboard/quizzes'
    ];

    for (const pagePath of pages) {
      await page.goto(pagePath);
      await page.waitForLoadState('networkidle');

      // Check for memory leaks by ensuring page is responsive
      const startTime = Date.now();
      await page.locator('body').click();
      const responseTime = Date.now() - startTime;

      // Page should remain responsive (< 100ms for click)
      expect(responseTime).toBeLessThan(100);
    }
  });

  test('should optimize image loading', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Check if images are loaded efficiently
    const images = page.locator('img');
    const imageCount = await images.count();

    if (imageCount > 0) {
      // Wait for images to load
      await page.waitForLoadState('networkidle');

      // Check if images have proper loading attributes
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const img = images.nth(i);
        const loading = await img.getAttribute('loading');
        const src = await img.getAttribute('src');

        // Images should have lazy loading or proper src
        expect(loading === 'lazy' || src !== null).toBe(true);
      }
    }
  });

  test('should handle network throttling', async ({ page }) => {
    // Simulate slow network
    await page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
      await route.continue();
    });

    const startTime = Date.now();

    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');

    const loadTime = Date.now() - startTime;

    // Should still load within reasonable time even with throttling
    expect(loadTime).toBeLessThan(8000);

    // Should show content
    const quizCards = page.locator('.quiz-card, .quiz-item');
    const cardCount = await quizCards.count();
    expect(cardCount).toBeGreaterThan(0);
  });

  test('should cache resources effectively', async ({ page }) => {
    // First visit
    const startTime1 = Date.now();
    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');
    const firstLoadTime = Date.now() - startTime1;

    // Second visit (should be faster due to caching)
    const startTime2 = Date.now();
    await page.reload();
    await page.waitForLoadState('networkidle');
    const secondLoadTime = Date.now() - startTime2;

    // Second load should be faster (allowing some variance)
    expect(secondLoadTime).toBeLessThan(firstLoadTime * 1.2);

    // Should still show content
    const quizCards = page.locator('.quiz-card, .quiz-item');
    const cardCount = await quizCards.count();
    expect(cardCount).toBeGreaterThan(0);
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Navigate to analytics which might have large datasets
    await page.goto('/dashboard/analytics');

    const startTime = Date.now();
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;

    // Analytics should load within 5 seconds even with large data
    expect(loadTime).toBeLessThan(5000);

    // Should show analytics content
    const analyticsContent = page.locator('.metric, .chart, .analytics-card');
    if (await analyticsContent.count() > 0) {
      await expect(analyticsContent.first()).toBeVisible();
    }
  });

  test('should optimize JavaScript execution', async ({ page }) => {
    await page.goto('/security-quizzes');

    // Measure JavaScript execution time
    const jsExecutionTime = await page.evaluate(() => {
      const start = performance.now();

      // Simulate some JavaScript operations
      for (let i = 0; i < 1000; i++) {
        document.querySelectorAll('.quiz-card, .quiz-item');
      }

      return performance.now() - start;
    });

    // JavaScript operations should be fast (< 50ms)
    expect(jsExecutionTime).toBeLessThan(50);
  });

  test('should handle form submissions efficiently', async ({ page }) => {
    // Test quiz creation form performance (admin only)
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    await page.goto('/dashboard/quizzes/create');

    // Fill form
    await page.fill('input[id="title"]', 'Performance Test Quiz');
    await page.fill('textarea[id="description"]', 'Testing form submission performance');

    const startTime = Date.now();

    // Submit form
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);

    const submissionTime = Date.now() - startTime;

    // Form submission should complete within 3 seconds
    expect(submissionTime).toBeLessThan(3000);

    // Should redirect to edit page
    await expect(page.locator('h1')).toContainText('Edit');
  });

  test('should handle API response times', async ({ page }) => {
    // Monitor network requests
    const apiRequests: Array<{ url: string; duration: number }> = [];

    page.on('response', async (response) => {
      if (response.url().includes('/api/')) {
        const request = response.request();
        const timing = response.timing();
        apiRequests.push({
          url: response.url(),
          duration: timing.responseEnd
        });
      }
    });

    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');

    // Check API response times
    for (const request of apiRequests) {
      // API calls should complete within 2 seconds
      expect(request.duration).toBeLessThan(2000);
    }
  });

  test('should optimize bundle size', async ({ page }) => {
    // Check for large JavaScript bundles
    const jsRequests: Array<{ url: string; size: number }> = [];

    page.on('response', async (response) => {
      if (response.url().endsWith('.js')) {
        const headers = response.headers();
        const contentLength = headers['content-length'];
        if (contentLength) {
          jsRequests.push({
            url: response.url(),
            size: parseInt(contentLength)
          });
        }
      }
    });

    await page.goto('/security-quizzes');
    await page.waitForLoadState('networkidle');

    // Check bundle sizes
    for (const request of jsRequests) {
      // Individual JS files should be under 1MB
      expect(request.size).toBeLessThan(1024 * 1024);
    }
  });
});
