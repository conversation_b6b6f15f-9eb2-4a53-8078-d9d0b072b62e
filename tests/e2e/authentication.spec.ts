import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing sessions
    await page.context().clearCookies();
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected dashboard
    await page.goto('/dashboard');
    
    // Should redirect to sign in page
    await page.waitForURL('/auth/signin');
    expect(page.url()).toContain('/auth/signin');
  });

  test('should allow admin login', async ({ page }) => {
    await page.goto('/auth/signin');
    
    // Fill in admin credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'admin123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');
    
    // Should show admin features
    await expect(page.locator('text=Create Quiz')).toBeVisible();
  });

  test('should allow user login', async ({ page }) => {
    await page.goto('/auth/signin');
    
    // Fill in user credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'user123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');
    
    // Should NOT show admin features
    await expect(page.locator('text=Create Quiz')).not.toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    await page.goto('/auth/signin');
    
    // Fill in invalid credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'wrongpassword');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show error message
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
    
    // Should stay on sign in page
    expect(page.url()).toContain('/auth/signin');
  });

  test('should allow user registration', async ({ page }) => {
    await page.goto('/auth/signup');
    
    // Fill in registration form
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', `test${Date.now()}@example.com`);
    await page.fill('input[name="password"]', 'testpassword123');
    await page.fill('input[name="confirmPassword"]', 'testpassword123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard or show success message
    await page.waitForURL('/dashboard');
    expect(page.url()).toContain('/dashboard');
  });

  test('should validate registration form', async ({ page }) => {
    await page.goto('/auth/signup');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('input[name="email"]:invalid')).toBeVisible();
    await expect(page.locator('input[name="password"]:invalid')).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    await page.goto('/auth/signup');
    
    // Fill in mismatched passwords
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.fill('input[name="confirmPassword"]', 'differentpassword');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show password mismatch error
    await expect(page.locator('text=Passwords do not match')).toBeVisible();
  });

  test('should allow logout', async ({ page }) => {
    // Login first
    await page.goto('/auth/signin');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/dashboard');
    
    // Click logout button
    await page.click('button:has-text("Logout")');
    
    // Should redirect to home page
    await page.waitForURL('/');
    expect(page.url()).toBe('http://localhost:3000/');
    
    // Should not be able to access dashboard
    await page.goto('/dashboard');
    await page.waitForURL('/auth/signin');
  });

  test('should persist session across page reloads', async ({ page }) => {
    // Login
    await page.goto('/auth/signin');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/dashboard');
    
    // Reload page
    await page.reload();
    
    // Should still be logged in
    expect(page.url()).toContain('/dashboard');
    await expect(page.locator('text=Create Quiz')).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    // Login
    await page.goto('/auth/signin');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'admin123');
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/dashboard');
    
    // Simulate session expiration by clearing cookies
    await page.context().clearCookies();
    
    // Try to access protected resource
    await page.goto('/dashboard/quizzes/create');
    
    // Should redirect to login
    await page.waitForURL('/auth/signin');
  });

  test('should restrict admin features to admin users', async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/signin');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'user123');
    await page.click('button[type="submit"]');
    
    await page.waitForURL('/dashboard');
    
    // Try to access quiz creation (admin only)
    await page.goto('/dashboard/quizzes/create');
    
    // Should be redirected or show access denied
    await expect(page.locator('text=Access denied')).toBeVisible();
  });
});
