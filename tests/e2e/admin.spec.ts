import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // Navigate to admin dashboard
    await page.goto('/dashboard/admin');
  });

  test('should display admin dashboard', async ({ page }) => {
    // Should show admin page title
    await expect(page.locator('h1')).toContainText('Admin');

    // Should show admin navigation or sections
    const adminSections = page.locator('.admin-section, .admin-panel, .admin-card');
    const sectionCount = await adminSections.count();
    expect(sectionCount).toBeGreaterThan(0);
  });

  test('should manage users', async ({ page }) => {
    // Navigate to user management
    const userManagementLink = page.locator('a[href*="users"], text=Users, text=User Management');
    if (await userManagementLink.isVisible()) {
      await userManagementLink.click();
      await page.waitForURL(/.*users.*/);

      // Should show user list
      await expect(page.locator('.user-list, .users-table, table')).toBeVisible();

      // Should show user actions
      const userActions = page.locator('button:has-text("Edit"), button:has-text("Delete"), button:has-text("Block")');
      if (await userActions.count() > 0) {
        await expect(userActions.first()).toBeVisible();
      }
    }
  });

  test('should create new user', async ({ page }) => {
    // Navigate to user management
    await page.goto('/dashboard/admin/users');

    // Look for create user button
    const createUserBtn = page.locator('button:has-text("Create User"), button:has-text("Add User")');
    if (await createUserBtn.isVisible()) {
      await createUserBtn.click();

      // Should show create user form
      await expect(page.locator('form, .user-form')).toBeVisible();

      // Fill in user details
      const nameInput = page.locator('input[id="name"], input[name="name"]');
      const emailInput = page.locator('input[id="email"], input[name="email"]');
      const roleSelect = page.locator('select[id="role"], select[name="role"]');

      if (await nameInput.isVisible()) {
        await nameInput.fill('Test User');
        await emailInput.fill(`testuser${Date.now()}@example.com`);

        if (await roleSelect.isVisible()) {
          await roleSelect.selectOption('user');
        }

        // Submit form
        const submitBtn = page.locator('button[type="submit"], button:has-text("Create")');
        await submitBtn.click();

        // Should show success message or redirect
        await expect(page.locator('text=User created, text=Success')).toBeVisible();
      }
    }
  });

  test('should manage quiz settings', async ({ page }) => {
    // Navigate to quiz management
    const quizManagementLink = page.locator('a[href*="quizzes"], text=Quiz Management');
    if (await quizManagementLink.isVisible()) {
      await quizManagementLink.click();

      // Should show quiz management interface
      await expect(page.locator('.quiz-management, .admin-quizzes')).toBeVisible();

      // Should show bulk actions
      const bulkActions = page.locator('button:has-text("Bulk"), .bulk-actions');
      if (await bulkActions.isVisible()) {
        await expect(bulkActions).toBeVisible();
      }
    }
  });

  test('should configure system settings', async ({ page }) => {
    // Look for settings section
    const settingsLink = page.locator('a[href*="settings"], text=Settings, text=Configuration');
    if (await settingsLink.isVisible()) {
      await settingsLink.click();

      // Should show settings form
      await expect(page.locator('form, .settings-form')).toBeVisible();

      // Should show various configuration options
      const configOptions = page.locator('input[type="checkbox"], input[type="radio"], select');
      if (await configOptions.count() > 0) {
        await expect(configOptions.first()).toBeVisible();
      }
    }
  });

  test('should view system logs', async ({ page }) => {
    // Look for logs section
    const logsLink = page.locator('a[href*="logs"], text=Logs, text=Activity Log');
    if (await logsLink.isVisible()) {
      await logsLink.click();

      // Should show logs table or list
      await expect(page.locator('.logs-table, .activity-log, table')).toBeVisible();

      // Should show log entries
      const logEntries = page.locator('.log-entry, tr');
      if (await logEntries.count() > 1) {
        await expect(logEntries.nth(1)).toBeVisible();
      }
    }
  });

  test('should manage categories and tags', async ({ page }) => {
    // Look for category management
    const categoriesLink = page.locator('a[href*="categories"], text=Categories, text=Tags');
    if (await categoriesLink.isVisible()) {
      await categoriesLink.click();

      // Should show category management interface
      await expect(page.locator('.category-management, .tags-management')).toBeVisible();

      // Should be able to add new category
      const addCategoryBtn = page.locator('button:has-text("Add Category"), button:has-text("New Category")');
      if (await addCategoryBtn.isVisible()) {
        await addCategoryBtn.click();

        const categoryInput = page.locator('input[placeholder*="category"], input[name*="category"]');
        if (await categoryInput.isVisible()) {
          await categoryInput.fill('Test Category');

          const saveBtn = page.locator('button:has-text("Save"), button:has-text("Add")');
          await saveBtn.click();

          // Should show success or new category in list
          await expect(page.locator('text=Test Category')).toBeVisible();
        }
      }
    }
  });

  test('should backup and restore data', async ({ page }) => {
    // Look for backup section
    const backupLink = page.locator('a[href*="backup"], text=Backup, text=Export');
    if (await backupLink.isVisible()) {
      await backupLink.click();

      // Should show backup options
      await expect(page.locator('.backup-section, .export-section')).toBeVisible();

      // Should be able to create backup
      const createBackupBtn = page.locator('button:has-text("Create Backup"), button:has-text("Export Data")');
      if (await createBackupBtn.isVisible()) {
        // Set up download handler
        const downloadPromise = page.waitForEvent('download');

        await createBackupBtn.click();

        // Wait for download to start
        try {
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toMatch(/\.(json|sql|zip)$/);
        } catch (error) {
          // Download might not be implemented yet
          console.log('Backup download not available');
        }
      }
    }
  });

  test('should monitor system health', async ({ page }) => {
    // Look for system health section
    const healthLink = page.locator('a[href*="health"], text=System Health, text=Status');
    if (await healthLink.isVisible()) {
      await healthLink.click();

      // Should show system status
      await expect(page.locator('.system-health, .status-dashboard')).toBeVisible();

      // Should show health indicators
      const healthIndicators = page.locator('.health-indicator, .status-indicator');
      if (await healthIndicators.count() > 0) {
        await expect(healthIndicators.first()).toBeVisible();
      }
    }
  });

  test('should manage permissions and roles', async ({ page }) => {
    // Look for permissions section
    const permissionsLink = page.locator('a[href*="permissions"], text=Permissions, text=Roles');
    if (await permissionsLink.isVisible()) {
      await permissionsLink.click();

      // Should show permissions management
      await expect(page.locator('.permissions-management, .roles-management')).toBeVisible();

      // Should show role list
      const roles = page.locator('.role-item, .permission-group');
      if (await roles.count() > 0) {
        await expect(roles.first()).toBeVisible();
      }
    }
  });

  test('should handle bulk operations', async ({ page }) => {
    // Navigate to user management for bulk operations
    await page.goto('/dashboard/admin/users');

    // Look for checkboxes to select multiple items
    const checkboxes = page.locator('input[type="checkbox"]');
    if (await checkboxes.count() > 2) {
      // Select multiple items
      await checkboxes.nth(1).check();
      await checkboxes.nth(2).check();

      // Look for bulk action dropdown
      const bulkActionSelect = page.locator('select[name*="bulk"], .bulk-actions select');
      if (await bulkActionSelect.isVisible()) {
        await bulkActionSelect.selectOption('delete');

        const confirmBtn = page.locator('button:has-text("Apply"), button:has-text("Execute")');
        if (await confirmBtn.isVisible()) {
          await confirmBtn.click();

          // Should show confirmation dialog
          await expect(page.locator('.confirmation-dialog, .modal')).toBeVisible();
        }
      }
    }
  });
});

test.describe('Admin Access Control', () => {
  test('should deny access to non-admin users', async ({ page }) => {
    // Login as regular user
    await page.goto('/auth/login');
    await page.fill('input[id="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', 'user123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // Try to access admin dashboard
    await page.goto('/dashboard/admin');

    // Should be redirected or show access denied
    const currentUrl = page.url();
    const isAccessDenied = currentUrl.includes('/dashboard') && !currentUrl.includes('/admin');
    const hasAccessDeniedMessage = await page.locator('text=Access denied, text=Unauthorized').isVisible();

    expect(isAccessDenied || hasAccessDeniedMessage).toBe(true);
  });
});
