# Test info

- Name: Authentication >> should restrict admin features to admin users
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:172:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:179:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   79 |
   80 |     // Submit form
   81 |     await page.click('button[type="submit"]');
   82 |
   83 |     // Should redirect to dashboard or show success message
   84 |     await page.waitForURL('/dashboard');
   85 |     expect(page.url()).toContain('/dashboard');
   86 |   });
   87 |
   88 |   test('should validate registration form', async ({ page }) => {
   89 |     await page.goto('/auth/register');
   90 |
   91 |     // Try to submit empty form
   92 |     await page.click('button[type="submit"]');
   93 |
   94 |     // Should show validation errors
   95 |     await expect(page.locator('input[id="email"]:invalid')).toBeVisible();
   96 |     await expect(page.locator('input[id="password"]:invalid')).toBeVisible();
   97 |   });
   98 |
   99 |   test('should validate password confirmation', async ({ page }) => {
  100 |     await page.goto('/auth/register');
  101 |
  102 |     // Fill in mismatched passwords
  103 |     await page.fill('input[id="name"]', 'Test User');
  104 |     await page.fill('input[id="email"]', '<EMAIL>');
  105 |     await page.fill('input[id="password"]', 'password123');
  106 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
  107 |
  108 |     // Submit form
  109 |     await page.click('button[type="submit"]');
  110 |
  111 |     // Should show password mismatch error
  112 |     await expect(page.locator('text=Passwords do not match')).toBeVisible();
  113 |   });
  114 |
  115 |   test('should allow logout', async ({ page }) => {
  116 |     // Login first
  117 |     await page.goto('/auth/login');
  118 |     await page.fill('input[id="email"]', '<EMAIL>');
  119 |     await page.fill('input[id="password"]', 'admin123');
  120 |     await page.click('button[type="submit"]');
  121 |
  122 |     await page.waitForURL('/dashboard');
  123 |
  124 |     // Click logout button
  125 |     await page.click('button:has-text("Logout")');
  126 |
  127 |     // Should redirect to home page
  128 |     await page.waitForURL('/');
  129 |     expect(page.url()).toBe('http://localhost:3000/');
  130 |
  131 |     // Should not be able to access dashboard
  132 |     await page.goto('/dashboard');
  133 |     await page.waitForURL('/auth/login');
  134 |   });
  135 |
  136 |   test('should persist session across page reloads', async ({ page }) => {
  137 |     // Login
  138 |     await page.goto('/auth/login');
  139 |     await page.fill('input[id="email"]', '<EMAIL>');
  140 |     await page.fill('input[id="password"]', 'admin123');
  141 |     await page.click('button[type="submit"]');
  142 |
  143 |     await page.waitForURL('/dashboard');
  144 |
  145 |     // Reload page
  146 |     await page.reload();
  147 |
  148 |     // Should still be logged in
  149 |     expect(page.url()).toContain('/dashboard');
  150 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
  151 |   });
  152 |
  153 |   test('should handle session expiration', async ({ page }) => {
  154 |     // Login
  155 |     await page.goto('/auth/login');
  156 |     await page.fill('input[id="email"]', '<EMAIL>');
  157 |     await page.fill('input[id="password"]', 'admin123');
  158 |     await page.click('button[type="submit"]');
  159 |
  160 |     await page.waitForURL('/dashboard');
  161 |
  162 |     // Simulate session expiration by clearing cookies
  163 |     await page.context().clearCookies();
  164 |
  165 |     // Try to access protected resource
  166 |     await page.goto('/dashboard/quizzes/create');
  167 |
  168 |     // Should redirect to login
  169 |     await page.waitForURL('/auth/login');
  170 |   });
  171 |
  172 |   test('should restrict admin features to admin users', async ({ page }) => {
  173 |     // Login as regular user
  174 |     await page.goto('/auth/login');
  175 |     await page.fill('input[id="email"]', '<EMAIL>');
  176 |     await page.fill('input[id="password"]', 'user123');
  177 |     await page.click('button[type="submit"]');
  178 |
> 179 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  180 |
  181 |     // Try to access quiz creation (admin only)
  182 |     await page.goto('/dashboard/quizzes/create');
  183 |
  184 |     // Should be redirected or show access denied
  185 |     await expect(page.locator('text=Access denied')).toBeVisible();
  186 |   });
  187 | });
  188 |
```