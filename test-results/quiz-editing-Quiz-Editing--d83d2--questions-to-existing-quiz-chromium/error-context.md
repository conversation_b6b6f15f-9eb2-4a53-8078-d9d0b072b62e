# Test info

- Name: Quiz Editing - All Possibilities >> should add questions to existing quiz
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:67:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=What is SQL injection?')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=What is SQL injection?')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:124:63
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Edit Quiz" [level=1]
  - button "Back to Quizzes"
  - button "Preview Quiz"
  - tablist:
    - tab "Quiz Details"
    - tab "Questions" [selected]
    - tab "Question Pools"
    - tab "Publish"
  - tabpanel "Questions":
    - heading "Questions" [level=3]
    - paragraph: Add, edit, or remove questions from your quiz
    - tablist:
      - tab "Existing Questions" [selected]
      - tab "Add Question"
    - tabpanel "Existing Questions":
      - paragraph: No questions yet. Add your first question to get started.
      - button "Add First Question"
- alert
```

# Test source

```ts
   24 |     
   25 |     // Save changes (auto-save or manual save)
   26 |     const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
   27 |     if (await saveBtn.isVisible()) {
   28 |       await saveBtn.click();
   29 |       await page.waitForTimeout(1000);
   30 |     }
   31 |     
   32 |     // Refresh page to verify changes were saved
   33 |     await page.reload();
   34 |     await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
   35 |     await expect(page.locator('textarea[id="description"]')).toHaveValue('Updated description with more details');
   36 |   });
   37 |
   38 |   test('should edit quiz settings', async ({ page }) => {
   39 |     // Create a quiz with initial settings
   40 |     await page.goto('/dashboard/quizzes/create');
   41 |     await page.fill('input[id="title"]', 'Settings Edit Test');
   42 |     await page.fill('textarea[id="description"]', 'Testing settings editing');
   43 |     await page.fill('input[id="passingScore"]', '70');
   44 |     await page.fill('input[id="timeLimit"]', '20');
   45 |     await page.click('button[type="submit"]');
   46 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
   47 |     
   48 |     // Edit the settings
   49 |     await page.fill('input[id="passingScore"]', '85');
   50 |     await page.fill('input[id="timeLimit"]', '45');
   51 |     await page.fill('input[id="tags"]', 'updated, settings, test');
   52 |     
   53 |     // Save changes
   54 |     const saveBtn = page.locator('button:has-text("Save")');
   55 |     if (await saveBtn.isVisible()) {
   56 |       await saveBtn.click();
   57 |       await page.waitForTimeout(1000);
   58 |     }
   59 |     
   60 |     // Verify changes
   61 |     await page.reload();
   62 |     await expect(page.locator('input[id="passingScore"]')).toHaveValue('85');
   63 |     await expect(page.locator('input[id="timeLimit"]')).toHaveValue('45');
   64 |     await expect(page.locator('input[id="tags"]')).toHaveValue('updated, settings, test');
   65 |   });
   66 |
   67 |   test('should add questions to existing quiz', async ({ page }) => {
   68 |     // Create a quiz without questions
   69 |     await page.goto('/dashboard/quizzes/create');
   70 |     await page.fill('input[id="title"]', 'Question Addition Test');
   71 |     await page.fill('textarea[id="description"]', 'Testing adding questions');
   72 |     await page.click('button[type="submit"]');
   73 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
   74 |     
   75 |     // Navigate to questions section
   76 |     await page.click('button:has-text("Questions"), .questions-tab');
   77 |     
   78 |     // Add first question
   79 |     await page.click('button:has-text("Add Question")');
   80 |     
   81 |     const questionTypeSelect = page.locator('select[id="questionType"]');
   82 |     if (await questionTypeSelect.isVisible()) {
   83 |       await questionTypeSelect.selectOption('multiple_choice');
   84 |       await page.fill('textarea[id="questionText"]', 'What is SQL injection?');
   85 |       await page.fill('input[id="points"]', '3');
   86 |       
   87 |       // Add options
   88 |       const optionInputs = page.locator('input[placeholder*="option"]');
   89 |       if (await optionInputs.count() >= 4) {
   90 |         await optionInputs.nth(0).fill('A database query attack');
   91 |         await optionInputs.nth(1).fill('A network protocol');
   92 |         await optionInputs.nth(2).fill('A programming language');
   93 |         await optionInputs.nth(3).fill('An encryption method');
   94 |       }
   95 |       
   96 |       // Mark correct answer
   97 |       const correctAnswerRadio = page.locator('input[type="radio"][name="correctOption"]');
   98 |       if (await correctAnswerRadio.count() > 0) {
   99 |         await correctAnswerRadio.first().check();
  100 |       }
  101 |       
  102 |       await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
  103 |       await page.waitForTimeout(1000);
  104 |     }
  105 |     
  106 |     // Add second question of different type
  107 |     await page.click('button:has-text("Add Question")');
  108 |     
  109 |     if (await questionTypeSelect.isVisible()) {
  110 |       await questionTypeSelect.selectOption('true_false');
  111 |       await page.fill('textarea[id="questionText"]', 'HTTPS is always secure.');
  112 |       
  113 |       const trueFalseOptions = page.locator('input[value="false"]');
  114 |       if (await trueFalseOptions.count() > 0) {
  115 |         await trueFalseOptions.first().check();
  116 |       }
  117 |       
  118 |       await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
  119 |       await page.waitForTimeout(1000);
  120 |     }
  121 |     
  122 |     // Verify questions were added
  123 |     await page.click('button:has-text("Existing Questions"), .existing-questions');
> 124 |     await expect(page.locator('text=What is SQL injection?')).toBeVisible();
      |                                                               ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  125 |     await expect(page.locator('text=HTTPS is always secure')).toBeVisible();
  126 |   });
  127 |
  128 |   test('should edit existing questions', async ({ page }) => {
  129 |     // Create quiz with a question first
  130 |     await page.goto('/dashboard/quizzes/create');
  131 |     await page.fill('input[id="title"]', 'Question Edit Test');
  132 |     await page.fill('textarea[id="description"]', 'Testing question editing');
  133 |     await page.click('button[type="submit"]');
  134 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  135 |     
  136 |     // Add initial question
  137 |     await page.click('button:has-text("Questions")');
  138 |     await page.click('button:has-text("Add Question")');
  139 |     
  140 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  141 |     if (await questionTypeSelect.isVisible()) {
  142 |       await questionTypeSelect.selectOption('multiple_choice');
  143 |       await page.fill('textarea[id="questionText"]', 'Original question text');
  144 |       
  145 |       const optionInputs = page.locator('input[placeholder*="option"]');
  146 |       if (await optionInputs.count() >= 2) {
  147 |         await optionInputs.nth(0).fill('Original Option A');
  148 |         await optionInputs.nth(1).fill('Original Option B');
  149 |       }
  150 |       
  151 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  152 |       await page.waitForTimeout(1000);
  153 |     }
  154 |     
  155 |     // Now edit the question
  156 |     await page.click('button:has-text("Existing Questions")');
  157 |     
  158 |     const editBtn = page.locator('button:has-text("Edit"), .edit-question, .question-edit');
  159 |     if (await editBtn.isVisible()) {
  160 |       await editBtn.first().click();
  161 |       
  162 |       // Edit question text
  163 |       await page.fill('textarea[id="questionText"]', 'Updated question text with more detail');
  164 |       
  165 |       // Edit options
  166 |       const optionInputs = page.locator('input[placeholder*="option"]');
  167 |       if (await optionInputs.count() >= 2) {
  168 |         await optionInputs.nth(0).fill('Updated Option A');
  169 |         await optionInputs.nth(1).fill('Updated Option B');
  170 |       }
  171 |       
  172 |       // Save changes
  173 |       await page.click('button:has-text("Save"), button:has-text("Update")');
  174 |       await page.waitForTimeout(1000);
  175 |       
  176 |       // Verify changes
  177 |       await expect(page.locator('text=Updated question text')).toBeVisible();
  178 |       await expect(page.locator('text=Updated Option A')).toBeVisible();
  179 |     }
  180 |   });
  181 |
  182 |   test('should delete questions from quiz', async ({ page }) => {
  183 |     // Create quiz with multiple questions
  184 |     await page.goto('/dashboard/quizzes/create');
  185 |     await page.fill('input[id="title"]', 'Question Deletion Test');
  186 |     await page.fill('textarea[id="description"]', 'Testing question deletion');
  187 |     await page.click('button[type="submit"]');
  188 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  189 |     
  190 |     // Add two questions
  191 |     await page.click('button:has-text("Questions")');
  192 |     
  193 |     for (let i = 1; i <= 2; i++) {
  194 |       await page.click('button:has-text("Add Question")');
  195 |       
  196 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  197 |       if (await questionTypeSelect.isVisible()) {
  198 |         await questionTypeSelect.selectOption('multiple_choice');
  199 |         await page.fill('textarea[id="questionText"]', `Question ${i} to be deleted`);
  200 |         
  201 |         const optionInputs = page.locator('input[placeholder*="option"]');
  202 |         if (await optionInputs.count() >= 2) {
  203 |           await optionInputs.nth(0).fill(`Option A${i}`);
  204 |           await optionInputs.nth(1).fill(`Option B${i}`);
  205 |         }
  206 |         
  207 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  208 |         await page.waitForTimeout(1000);
  209 |       }
  210 |     }
  211 |     
  212 |     // Delete first question
  213 |     await page.click('button:has-text("Existing Questions")');
  214 |     
  215 |     const deleteBtn = page.locator('button:has-text("Delete"), .delete-question, .question-delete');
  216 |     if (await deleteBtn.isVisible()) {
  217 |       await deleteBtn.first().click();
  218 |       
  219 |       // Confirm deletion if confirmation dialog appears
  220 |       const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes"), button:has-text("Delete")');
  221 |       if (await confirmBtn.isVisible()) {
  222 |         await confirmBtn.click();
  223 |         await page.waitForTimeout(1000);
  224 |       }
```