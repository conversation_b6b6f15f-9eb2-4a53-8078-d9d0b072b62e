# Test info

- Name: Quiz Search and Discovery >> should handle empty search results
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-search.spec.ts:134:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-search.spec.ts:10:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Quiz Search and Discovery', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as regular user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'user123');
   9 |     await page.click('button[type="submit"]');
>  10 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   11 |   });
   12 |
   13 |   test('should search quizzes by title', async ({ page }) => {
   14 |     // Navigate to quiz browse page
   15 |     await page.goto('/security-quizzes');
   16 |
   17 |     // Look for search input
   18 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
   19 |     if (await searchInput.isVisible()) {
   20 |       // Search for specific term
   21 |       await searchInput.fill('security');
   22 |       await searchInput.press('Enter');
   23 |
   24 |       // Wait for search results
   25 |       await page.waitForTimeout(1000);
   26 |
   27 |       // Should show filtered results
   28 |       const quizCards = page.locator('.quiz-card, .quiz-item');
   29 |       if (await quizCards.count() > 0) {
   30 |         // Verify results contain search term
   31 |         const firstCard = quizCards.first();
   32 |         const cardText = await firstCard.textContent();
   33 |         expect(cardText?.toLowerCase()).toContain('security');
   34 |       }
   35 |     }
   36 |   });
   37 |
   38 |   test('should filter quizzes by category', async ({ page }) => {
   39 |     await page.goto('/security-quizzes');
   40 |
   41 |     // Look for category filter
   42 |     const categoryFilter = page.locator('select[name*="category"], .category-filter select');
   43 |     if (await categoryFilter.isVisible()) {
   44 |       // Select a category
   45 |       await categoryFilter.selectOption('Web Application Security');
   46 |
   47 |       // Wait for filtering
   48 |       await page.waitForTimeout(1000);
   49 |
   50 |       // Should show filtered results
   51 |       const quizCards = page.locator('.quiz-card, .quiz-item');
   52 |       if (await quizCards.count() > 0) {
   53 |         await expect(quizCards.first()).toBeVisible();
   54 |       }
   55 |     }
   56 |   });
   57 |
   58 |   test('should filter quizzes by difficulty', async ({ page }) => {
   59 |     await page.goto('/security-quizzes');
   60 |
   61 |     // Look for difficulty filter
   62 |     const difficultyFilter = page.locator('select[name*="difficulty"], .difficulty-filter select');
   63 |     if (await difficultyFilter.isVisible()) {
   64 |       // Select difficulty level
   65 |       await difficultyFilter.selectOption('Beginner');
   66 |
   67 |       // Wait for filtering
   68 |       await page.waitForTimeout(1000);
   69 |
   70 |       // Should show filtered results
   71 |       const quizCards = page.locator('.quiz-card, .quiz-item');
   72 |       if (await quizCards.count() > 0) {
   73 |         // Verify difficulty is shown
   74 |         const difficultyBadges = page.locator('text=Beginner, .difficulty-badge');
   75 |         if (await difficultyBadges.count() > 0) {
   76 |           await expect(difficultyBadges.first()).toBeVisible();
   77 |         }
   78 |       }
   79 |     }
   80 |   });
   81 |
   82 |   test('should sort quizzes', async ({ page }) => {
   83 |     await page.goto('/security-quizzes');
   84 |
   85 |     // Look for sort dropdown
   86 |     const sortSelect = page.locator('select[name*="sort"], .sort-select select');
   87 |     if (await sortSelect.isVisible()) {
   88 |       // Get initial order
   89 |       const initialQuizzes = await page.locator('.quiz-card h3, .quiz-title').allTextContents();
   90 |
   91 |       // Change sort order
   92 |       await sortSelect.selectOption('title');
   93 |       await page.waitForTimeout(1000);
   94 |
   95 |       // Get new order
   96 |       const sortedQuizzes = await page.locator('.quiz-card h3, .quiz-title').allTextContents();
   97 |
   98 |       // Verify order changed (basic check)
   99 |       expect(sortedQuizzes).not.toEqual(initialQuizzes);
  100 |     }
  101 |   });
  102 |
  103 |   test('should show quiz details in search results', async ({ page }) => {
  104 |     await page.goto('/security-quizzes');
  105 |
  106 |     // Should show quiz cards with details
  107 |     const quizCards = page.locator('.quiz-card, .quiz-item');
  108 |     if (await quizCards.count() > 0) {
  109 |       const firstCard = quizCards.first();
  110 |
```