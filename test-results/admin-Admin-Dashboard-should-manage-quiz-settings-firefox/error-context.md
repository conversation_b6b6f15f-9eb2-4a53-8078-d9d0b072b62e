# Test info

- Name: Ad<PERSON> >> should manage quiz settings
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:80:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:92:28
```

# Page snapshot

```yaml
- alert
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Home":
      - /url: /landing
    - link "Explore":
      - /url: /explore
    - link "Security Quizzes":
      - /url: /security-quizzes
  - link "Dashboard":
    - /url: /dashboard
- heading "Create, Share, and Take Interactive Quizzes" [level=1]
- paragraph: QuizFlow is a standardized, flexible, and interactive quiz ecosystem for creating and sharing knowledge assessments.
- link "Create a Quiz":
  - /url: /dashboard/quizzes/create
- link "Explore Quizzes":
  - /url: /explore
- heading "Key Features" [level=2]
- heading "Standardized Format" [level=3]
- paragraph: Create quizzes using our well-documented, extensible, and universally understandable JSON format.
- heading "Rich Interactivity" [level=3]
- paragraph: Support for multiple question types, media integration, and dynamic content for engaging learning experiences.
- heading "Open Ecosystem" [level=3]
- paragraph: An open-source foundation that encourages community involvement and integration with other systems.
- heading "Ready to get started?" [level=2]
- paragraph: Join QuizFlow today and explore interactive quizzes for education, training, or fun.
- link "Go to Dashboard":
  - /url: /dashboard
- contentinfo:
  - heading "QuizFlow" [level=3]
  - paragraph: A standardized, flexible, and interactive quiz ecosystem.
  - heading "Product" [level=4]
  - list:
    - listitem:
      - link "Features":
        - /url: /features
    - listitem:
      - link "Explore":
        - /url: /explore
    - listitem:
      - link "Pricing":
        - /url: /pricing
  - heading "Resources" [level=4]
  - list:
    - listitem:
      - link "Documentation":
        - /url: /docs
    - listitem:
      - link "Guides":
        - /url: /guides
    - listitem:
      - link "API":
        - /url: /api
  - heading "Company" [level=4]
  - list:
    - listitem:
      - link "About":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact":
        - /url: /contact
  - paragraph: © 2025 QuizFlow. All rights reserved.
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Admin Dashboard', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as admin user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'admin123');
   9 |     await page.click('button[type="submit"]');
   10 |     await page.waitForURL('/dashboard');
   11 |
   12 |     // Navigate to admin dashboard
   13 |     await page.goto('/dashboard/admin');
   14 |   });
   15 |
   16 |   test('should display admin dashboard', async ({ page }) => {
   17 |     // Should show some page content (admin dashboard might not exist yet)
   18 |     await expect(page.locator('h1')).toBeVisible();
   19 |
   20 |     // Should show some navigation or content
   21 |     const content = page.locator('main, .content, nav, .navigation');
   22 |     const contentCount = await content.count();
   23 |     expect(contentCount).toBeGreaterThan(0);
   24 |   });
   25 |
   26 |   test('should manage users', async ({ page }) => {
   27 |     // Look for user management links
   28 |     const userLinks = page.locator('a:has-text("Users"), a:has-text("User Management"), a[href*="users"]');
   29 |     const userLinkCount = await userLinks.count();
   30 |
   31 |     if (userLinkCount > 0) {
   32 |       await userLinks.first().click();
   33 |       await page.waitForTimeout(1000);
   34 |
   35 |       // Should show some content (user list, table, or form)
   36 |       const userContent = page.locator('.user-list, .users-table, table, form, .content');
   37 |       const contentCount = await userContent.count();
   38 |       expect(contentCount).toBeGreaterThan(0);
   39 |     } else {
   40 |       // User management might not be implemented yet - that's ok
   41 |       console.log('User management not found - feature may not be implemented yet');
   42 |     }
   43 |   });
   44 |
   45 |   test('should create new user', async ({ page }) => {
   46 |     // Navigate to user management
   47 |     await page.goto('/dashboard/admin/users');
   48 |
   49 |     // Look for create user button
   50 |     const createUserBtn = page.locator('button:has-text("Create User"), button:has-text("Add User")');
   51 |     if (await createUserBtn.isVisible()) {
   52 |       await createUserBtn.click();
   53 |
   54 |       // Should show create user form
   55 |       await expect(page.locator('form, .user-form')).toBeVisible();
   56 |
   57 |       // Fill in user details
   58 |       const nameInput = page.locator('input[id="name"], input[name="name"]');
   59 |       const emailInput = page.locator('input[id="email"], input[name="email"]');
   60 |       const roleSelect = page.locator('select[id="role"], select[name="role"]');
   61 |
   62 |       if (await nameInput.isVisible()) {
   63 |         await nameInput.fill('Test User');
   64 |         await emailInput.fill(`testuser${Date.now()}@example.com`);
   65 |
   66 |         if (await roleSelect.isVisible()) {
   67 |           await roleSelect.selectOption('user');
   68 |         }
   69 |
   70 |         // Submit form
   71 |         const submitBtn = page.locator('button[type="submit"], button:has-text("Create")');
   72 |         await submitBtn.click();
   73 |
   74 |         // Should show success message or redirect
   75 |         await expect(page.locator('text=User created, text=Success')).toBeVisible();
   76 |       }
   77 |     }
   78 |   });
   79 |
   80 |   test('should manage quiz settings', async ({ page }) => {
   81 |     // Look for quiz management links
   82 |     const quizLinks = page.locator('a:has-text("Quiz"), a:has-text("Quizzes"), a[href*="quizzes"]');
   83 |     const quizLinkCount = await quizLinks.count();
   84 |
   85 |     if (quizLinkCount > 0) {
   86 |       await quizLinks.first().click();
   87 |       await page.waitForTimeout(1000);
   88 |
   89 |       // Should show some content
   90 |       const content = page.locator('.quiz-management, .admin-quizzes, .content, main');
   91 |       const contentCount = await content.count();
>  92 |       expect(contentCount).toBeGreaterThan(0);
      |                            ^ Error: expect(received).toBeGreaterThan(expected)
   93 |     } else {
   94 |       console.log('Quiz management not found - feature may not be implemented yet');
   95 |     }
   96 |   });
   97 |
   98 |   test('should configure system settings', async ({ page }) => {
   99 |     // Look for settings section
  100 |     const settingsLinks = page.locator('a:has-text("Settings"), a:has-text("Configuration"), a[href*="settings"]');
  101 |     const settingsCount = await settingsLinks.count();
  102 |
  103 |     if (settingsCount > 0) {
  104 |       await settingsLinks.first().click();
  105 |       await page.waitForTimeout(1000);
  106 |
  107 |       // Should show some content
  108 |       const content = page.locator('form, .settings-form, .content, main');
  109 |       const contentCount = await content.count();
  110 |       expect(contentCount).toBeGreaterThan(0);
  111 |     } else {
  112 |       console.log('Settings not found - feature may not be implemented yet');
  113 |     }
  114 |   });
  115 |
  116 |   test('should view system logs', async ({ page }) => {
  117 |     // Look for logs section
  118 |     const logsLinks = page.locator('a:has-text("Logs"), a:has-text("Activity"), a[href*="logs"]');
  119 |     const logsCount = await logsLinks.count();
  120 |
  121 |     if (logsCount > 0) {
  122 |       await logsLinks.first().click();
  123 |       await page.waitForTimeout(1000);
  124 |
  125 |       // Should show some content
  126 |       const content = page.locator('.logs-table, .activity-log, table, .content, main');
  127 |       const contentCount = await content.count();
  128 |       expect(contentCount).toBeGreaterThan(0);
  129 |     } else {
  130 |       console.log('Logs not found - feature may not be implemented yet');
  131 |     }
  132 |   });
  133 |
  134 |   test('should manage categories and tags', async ({ page }) => {
  135 |     // Look for category management
  136 |     const categoryLinks = page.locator('a:has-text("Categories"), a:has-text("Tags"), a[href*="categories"]');
  137 |     const categoryCount = await categoryLinks.count();
  138 |
  139 |     if (categoryCount > 0) {
  140 |       await categoryLinks.first().click();
  141 |       await page.waitForTimeout(1000);
  142 |
  143 |       // Should show some content
  144 |       const content = page.locator('.category-management, .tags-management, .content, main');
  145 |       const contentCount = await content.count();
  146 |       expect(contentCount).toBeGreaterThan(0);
  147 |     } else {
  148 |       console.log('Category management not found - feature may not be implemented yet');
  149 |     }
  150 |   });
  151 |
  152 |   test('should backup and restore data', async ({ page }) => {
  153 |     // Look for backup section
  154 |     const backupLinks = page.locator('a:has-text("Backup"), a:has-text("Export"), a[href*="backup"]');
  155 |     const backupCount = await backupLinks.count();
  156 |
  157 |     if (backupCount > 0) {
  158 |       await backupLinks.first().click();
  159 |       await page.waitForTimeout(1000);
  160 |
  161 |       // Should show some content
  162 |       const content = page.locator('.backup-section, .export-section, .content, main');
  163 |       const contentCount = await content.count();
  164 |       expect(contentCount).toBeGreaterThan(0);
  165 |     } else {
  166 |       console.log('Backup functionality not found - feature may not be implemented yet');
  167 |     }
  168 |   });
  169 |
  170 |   test('should monitor system health', async ({ page }) => {
  171 |     // Look for system health section
  172 |     const healthLinks = page.locator('a:has-text("Health"), a:has-text("Status"), a[href*="health"]');
  173 |     const healthCount = await healthLinks.count();
  174 |
  175 |     if (healthCount > 0) {
  176 |       await healthLinks.first().click();
  177 |       await page.waitForTimeout(1000);
  178 |
  179 |       // Should show some content
  180 |       const content = page.locator('.system-health, .status-dashboard, .content, main');
  181 |       const contentCount = await content.count();
  182 |       expect(contentCount).toBeGreaterThan(0);
  183 |     } else {
  184 |       console.log('System health monitoring not found - feature may not be implemented yet');
  185 |     }
  186 |   });
  187 |
  188 |   test('should manage permissions and roles', async ({ page }) => {
  189 |     // Look for permissions section
  190 |     const permissionLinks = page.locator('a:has-text("Permissions"), a:has-text("Roles"), a[href*="permissions"]');
  191 |     const permissionCount = await permissionLinks.count();
  192 |
```