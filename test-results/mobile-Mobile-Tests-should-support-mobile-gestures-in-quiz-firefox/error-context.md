# Test info

- Name: Mobile Tests >> should support mobile gestures in quiz
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:320:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:326:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  226 |         await expect(page.locator('.question-container, .question')).toBeVisible();
  227 |         
  228 |         // Answer options should be touch-friendly
  229 |         const options = page.locator('input[type="radio"], .option');
  230 |         if (await options.count() > 0) {
  231 |           const optionBox = await options.first().boundingBox();
  232 |           
  233 |           if (optionBox) {
  234 |             expect(optionBox.height).toBeGreaterThanOrEqual(40);
  235 |           }
  236 |         }
  237 |       }
  238 |     }
  239 |   });
  240 |
  241 |   test('should handle mobile orientation changes', async ({ page }) => {
  242 |     // Start in portrait
  243 |     await page.setViewportSize({ width: 375, height: 667 });
  244 |     await page.goto('/security-quizzes');
  245 |     
  246 |     await expect(page.locator('h1')).toBeVisible();
  247 |     
  248 |     // Switch to landscape
  249 |     await page.setViewportSize({ width: 667, height: 375 });
  250 |     await page.waitForTimeout(500);
  251 |     
  252 |     // Should still display content properly
  253 |     await expect(page.locator('h1')).toBeVisible();
  254 |     
  255 |     // Content should adapt to landscape
  256 |     const quizCards = page.locator('.quiz-card, .quiz-item');
  257 |     if (await quizCards.count() > 1) {
  258 |       const firstCard = quizCards.first();
  259 |       const secondCard = quizCards.nth(1);
  260 |       
  261 |       const firstBox = await firstCard.boundingBox();
  262 |       const secondBox = await secondCard.boundingBox();
  263 |       
  264 |       if (firstBox && secondBox) {
  265 |         // In landscape, cards might be side by side
  266 |         const isSideBySide = Math.abs(firstBox.y - secondBox.y) < 50;
  267 |         const isStacked = secondBox.y > firstBox.y + firstBox.height - 50;
  268 |         
  269 |         expect(isSideBySide || isStacked).toBe(true);
  270 |       }
  271 |     }
  272 |   });
  273 |
  274 |   test('should optimize mobile performance', async ({ page }) => {
  275 |     const startTime = Date.now();
  276 |     
  277 |     await page.goto('/security-quizzes');
  278 |     await page.waitForLoadState('networkidle');
  279 |     
  280 |     const loadTime = Date.now() - startTime;
  281 |     
  282 |     // Mobile should load within 5 seconds
  283 |     expect(loadTime).toBeLessThan(5000);
  284 |     
  285 |     // Should show content
  286 |     await expect(page.locator('.quiz-card, .quiz-item')).toHaveCount.greaterThan(0);
  287 |   });
  288 |
  289 |   test('should handle mobile search', async ({ page }) => {
  290 |     await page.goto('/auth/login');
  291 |     await page.fill('input[id="email"]', '<EMAIL>');
  292 |     await page.fill('input[id="password"]', 'user123');
  293 |     await page.click('button[type="submit"]');
  294 |     await page.waitForURL('/dashboard');
  295 |     
  296 |     await page.goto('/security-quizzes');
  297 |     
  298 |     // Look for search functionality
  299 |     const searchInput = page.locator('input[type="search"], input[placeholder*="search"]');
  300 |     if (await searchInput.isVisible()) {
  301 |       // Should be properly sized for mobile
  302 |       const searchBox = await searchInput.boundingBox();
  303 |       
  304 |       if (searchBox) {
  305 |         expect(searchBox.height).toBeGreaterThanOrEqual(40);
  306 |       }
  307 |       
  308 |       // Test search functionality
  309 |       await searchInput.fill('security');
  310 |       await searchInput.press('Enter');
  311 |       
  312 |       await page.waitForTimeout(1000);
  313 |       
  314 |       // Should show search results
  315 |       const results = page.locator('.quiz-card, .quiz-item, .no-results');
  316 |       await expect(results).toHaveCount.greaterThan(0);
  317 |     }
  318 |   });
  319 |
  320 |   test('should support mobile gestures in quiz', async ({ page }) => {
  321 |     // Login and start a quiz
  322 |     await page.goto('/auth/login');
  323 |     await page.fill('input[id="email"]', '<EMAIL>');
  324 |     await page.fill('input[id="password"]', 'user123');
  325 |     await page.click('button[type="submit"]');
> 326 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  327 |     
  328 |     await page.goto('/security-quizzes');
  329 |     
  330 |     const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
  331 |     if (await firstQuiz.isVisible()) {
  332 |       await firstQuiz.click();
  333 |       
  334 |       const startBtn = page.locator('button:has-text("Start Quiz")');
  335 |       if (await startBtn.isVisible()) {
  336 |         await startBtn.click();
  337 |         
  338 |         // Test swipe to next question (if supported)
  339 |         const questionContainer = page.locator('.question-container, .quiz-content');
  340 |         if (await questionContainer.isVisible()) {
  341 |           const containerBox = await questionContainer.boundingBox();
  342 |           
  343 |           if (containerBox) {
  344 |             // Simulate swipe left gesture
  345 |             await page.mouse.move(containerBox.x + containerBox.width * 0.8, containerBox.y + containerBox.height / 2);
  346 |             await page.mouse.down();
  347 |             await page.mouse.move(containerBox.x + containerBox.width * 0.2, containerBox.y + containerBox.height / 2);
  348 |             await page.mouse.up();
  349 |             
  350 |             await page.waitForTimeout(500);
  351 |             
  352 |             // Should still show quiz content
  353 |             await expect(questionContainer).toBeVisible();
  354 |           }
  355 |         }
  356 |       }
  357 |     }
  358 |   });
  359 | });
  360 |
```