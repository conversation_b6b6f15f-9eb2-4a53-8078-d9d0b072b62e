# Test info

- Name: Quiz Editing - All Possibilities >> should edit quiz with different question types
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:281:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('input[placeholder*="answer"], textarea[placeholder*="answer"]') resolved to 3 elements:
    1) <input value="" required="" type="text" class="flex-1 p-2 border rounded-md" placeholder="Enter a correct answer"/> aka getByRole('textbox', { name: 'Enter a correct answer' })
    2) <textarea id="feedbackCorrect" class="w-full p-2 border rounded-md" placeholder="Feedback to show when the answer is correct"></textarea> aka getByRole('textbox', { name: 'Feedback for Correct Answer' })
    3) <textarea id="feedbackIncorrect" class="w-full p-2 border rounded-md" placeholder="Feedback to show when the answer is incorrect"></textarea> aka getByRole('textbox', { name: 'Feedback for Incorrect Answer' })

Call log:
    - checking visibility of locator('input[placeholder*="answer"], textarea[placeholder*="answer"]')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:314:33
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Edit Quiz" [level=1]
  - button "Back to Quizzes"
  - button "Preview Quiz"
  - tablist:
    - tab "Quiz Details"
    - tab "Questions" [selected]
    - tab "Question Pools"
    - tab "Publish"
  - tabpanel "Questions":
    - heading "Questions" [level=3]
    - paragraph: Add, edit, or remove questions from your quiz
    - tablist:
      - tab "Existing Questions"
      - tab "Add Question" [selected]
    - tabpanel "Add Question":
      - text: Question Type
      - combobox "Question Type":
        - option "Multiple Choice"
        - option "True/False"
        - option "Short Answer" [selected]
        - option "Matching"
        - option "Fill in the Blank"
        - option "Essay"
      - heading "Add New Question" [level=3]
      - text: Question Text
      - textbox "Question Text": short_answer question for editing
      - text: Points
      - spinbutton "Points": "1"
      - text: Correct Answers
      - paragraph: Add multiple acceptable answers. The question will be marked correct if the student's answer matches any of these.
      - textbox "Enter a correct answer"
      - button "Remove" [disabled]:
        - text: Remove
        - img
      - button "Add Another Correct Answer"
      - text: Answer Options
      - checkbox "Case Sensitive"
      - text: Case Sensitive
      - paragraph: If checked, "Answer" and "answer" will be treated as different answers.
      - checkbox "Trim Whitespace" [checked]
      - text: Trim Whitespace
      - paragraph: If checked, leading and trailing spaces will be ignored.
      - checkbox "Exact Match Required" [checked]
      - text: Exact Match Required
      - paragraph: If unchecked, partial matches or keyword detection may be used (implementation dependent).
      - text: Feedback for Correct Answer
      - textbox "Feedback for Correct Answer"
      - text: Feedback for Incorrect Answer
      - textbox "Feedback for Incorrect Answer"
      - button "Add Question"
- alert
```

# Test source

```ts
  214 |     
  215 |     const deleteBtn = page.locator('button:has-text("Delete"), .delete-question, .question-delete');
  216 |     if (await deleteBtn.isVisible()) {
  217 |       await deleteBtn.first().click();
  218 |       
  219 |       // Confirm deletion if confirmation dialog appears
  220 |       const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes"), button:has-text("Delete")');
  221 |       if (await confirmBtn.isVisible()) {
  222 |         await confirmBtn.click();
  223 |         await page.waitForTimeout(1000);
  224 |       }
  225 |       
  226 |       // Verify question was deleted
  227 |       await expect(page.locator('text=Question 1 to be deleted')).not.toBeVisible();
  228 |       await expect(page.locator('text=Question 2 to be deleted')).toBeVisible();
  229 |     }
  230 |   });
  231 |
  232 |   test('should reorder questions in quiz', async ({ page }) => {
  233 |     // Create quiz with multiple questions
  234 |     await page.goto('/dashboard/quizzes/create');
  235 |     await page.fill('input[id="title"]', 'Question Reorder Test');
  236 |     await page.fill('textarea[id="description"]', 'Testing question reordering');
  237 |     await page.click('button[type="submit"]');
  238 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  239 |     
  240 |     // Add three questions
  241 |     await page.click('button:has-text("Questions")');
  242 |     
  243 |     for (let i = 1; i <= 3; i++) {
  244 |       await page.click('button:has-text("Add Question")');
  245 |       
  246 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  247 |       if (await questionTypeSelect.isVisible()) {
  248 |         await questionTypeSelect.selectOption('multiple_choice');
  249 |         await page.fill('textarea[id="questionText"]', `Question ${i} for reordering`);
  250 |         
  251 |         const optionInputs = page.locator('input[placeholder*="option"]');
  252 |         if (await optionInputs.count() >= 2) {
  253 |           await optionInputs.nth(0).fill(`Q${i} Option A`);
  254 |           await optionInputs.nth(1).fill(`Q${i} Option B`);
  255 |         }
  256 |         
  257 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  258 |         await page.waitForTimeout(1000);
  259 |       }
  260 |     }
  261 |     
  262 |     // Test reordering (look for drag handles or move up/down buttons)
  263 |     await page.click('button:has-text("Existing Questions")');
  264 |     
  265 |     const moveUpBtn = page.locator('button:has-text("Move Up"), .move-up, .question-up');
  266 |     const moveDownBtn = page.locator('button:has-text("Move Down"), .move-down, .question-down');
  267 |     
  268 |     if (await moveUpBtn.count() > 0 || await moveDownBtn.count() > 0) {
  269 |       // Try to move second question up
  270 |       if (await moveUpBtn.count() >= 2) {
  271 |         await moveUpBtn.nth(1).click();
  272 |         await page.waitForTimeout(1000);
  273 |       }
  274 |       
  275 |       // Verify order changed (this is a basic check)
  276 |       const questionTexts = await page.locator('.question-text, .question-title').allTextContents();
  277 |       expect(questionTexts.length).toBeGreaterThan(0);
  278 |     }
  279 |   });
  280 |
  281 |   test('should edit quiz with different question types', async ({ page }) => {
  282 |     // Create quiz and add various question types
  283 |     await page.goto('/dashboard/quizzes/create');
  284 |     await page.fill('input[id="title"]', 'Multi-Type Edit Test');
  285 |     await page.fill('textarea[id="description"]', 'Testing editing with different question types');
  286 |     await page.click('button[type="submit"]');
  287 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  288 |     
  289 |     await page.click('button:has-text("Questions")');
  290 |     
  291 |     const questionTypes = ['multiple_choice', 'true_false', 'short_answer'];
  292 |     
  293 |     for (let i = 0; i < questionTypes.length; i++) {
  294 |       await page.click('button:has-text("Add Question")');
  295 |       
  296 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  297 |       if (await questionTypeSelect.isVisible()) {
  298 |         await questionTypeSelect.selectOption(questionTypes[i]);
  299 |         await page.fill('textarea[id="questionText"]', `${questionTypes[i]} question for editing`);
  300 |         
  301 |         if (questionTypes[i] === 'multiple_choice') {
  302 |           const optionInputs = page.locator('input[placeholder*="option"]');
  303 |           if (await optionInputs.count() >= 2) {
  304 |             await optionInputs.nth(0).fill('MC Option A');
  305 |             await optionInputs.nth(1).fill('MC Option B');
  306 |           }
  307 |         } else if (questionTypes[i] === 'true_false') {
  308 |           const trueFalseOptions = page.locator('input[value="true"]');
  309 |           if (await trueFalseOptions.count() > 0) {
  310 |             await trueFalseOptions.first().check();
  311 |           }
  312 |         } else if (questionTypes[i] === 'short_answer') {
  313 |           const answerInput = page.locator('input[placeholder*="answer"], textarea[placeholder*="answer"]');
> 314 |           if (await answerInput.isVisible()) {
      |                                 ^ Error: locator.isVisible: Error: strict mode violation: locator('input[placeholder*="answer"], textarea[placeholder*="answer"]') resolved to 3 elements:
  315 |             await answerInput.fill('Sample correct answer');
  316 |           }
  317 |         }
  318 |         
  319 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  320 |         await page.waitForTimeout(1000);
  321 |       }
  322 |     }
  323 |     
  324 |     // Verify all question types were added
  325 |     await page.click('button:has-text("Existing Questions")');
  326 |     await expect(page.locator('text=multiple_choice question')).toBeVisible();
  327 |     await expect(page.locator('text=true_false question')).toBeVisible();
  328 |     await expect(page.locator('text=short_answer question')).toBeVisible();
  329 |   });
  330 |
  331 |   test('should handle quiz publishing and unpublishing', async ({ page }) => {
  332 |     // Create a complete quiz
  333 |     await page.goto('/dashboard/quizzes/create');
  334 |     await page.fill('input[id="title"]', 'Publishing Test Quiz');
  335 |     await page.fill('textarea[id="description"]', 'Testing quiz publishing functionality');
  336 |     await page.click('button[type="submit"]');
  337 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  338 |     
  339 |     // Add at least one question
  340 |     await page.click('button:has-text("Questions")');
  341 |     await page.click('button:has-text("Add Question")');
  342 |     
  343 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  344 |     if (await questionTypeSelect.isVisible()) {
  345 |       await questionTypeSelect.selectOption('multiple_choice');
  346 |       await page.fill('textarea[id="questionText"]', 'Ready to publish question');
  347 |       
  348 |       const optionInputs = page.locator('input[placeholder*="option"]');
  349 |       if (await optionInputs.count() >= 2) {
  350 |         await optionInputs.nth(0).fill('Publish Option A');
  351 |         await optionInputs.nth(1).fill('Publish Option B');
  352 |       }
  353 |       
  354 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  355 |       await page.waitForTimeout(1000);
  356 |     }
  357 |     
  358 |     // Test publishing
  359 |     const publishBtn = page.locator('button:has-text("Publish"), .publish-quiz');
  360 |     if (await publishBtn.isVisible()) {
  361 |       await publishBtn.click();
  362 |       
  363 |       // Confirm publishing if confirmation dialog appears
  364 |       const confirmBtn = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
  365 |       if (await confirmBtn.isVisible()) {
  366 |         await confirmBtn.click();
  367 |         await page.waitForTimeout(1000);
  368 |       }
  369 |       
  370 |       // Should show published status
  371 |       await expect(page.locator('text=Published, .published-status')).toBeVisible();
  372 |       
  373 |       // Test unpublishing
  374 |       const unpublishBtn = page.locator('button:has-text("Unpublish"), .unpublish-quiz');
  375 |       if (await unpublishBtn.isVisible()) {
  376 |         await unpublishBtn.click();
  377 |         
  378 |         const confirmUnpublish = page.locator('button:has-text("Confirm"), button:has-text("Yes")');
  379 |         if (await confirmUnpublish.isVisible()) {
  380 |           await confirmUnpublish.click();
  381 |           await page.waitForTimeout(1000);
  382 |         }
  383 |         
  384 |         // Should show draft status
  385 |         await expect(page.locator('text=Draft, .draft-status')).toBeVisible();
  386 |       }
  387 |     }
  388 |   });
  389 |
  390 |   test('should validate quiz before saving', async ({ page }) => {
  391 |     // Create quiz and test validation
  392 |     await page.goto('/dashboard/quizzes/create');
  393 |     await page.fill('input[id="title"]', 'Validation Test Quiz');
  394 |     await page.fill('textarea[id="description"]', 'Testing validation');
  395 |     await page.click('button[type="submit"]');
  396 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  397 |     
  398 |     // Try to clear required fields and save
  399 |     await page.fill('input[id="title"]', '');
  400 |     
  401 |     const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
  402 |     if (await saveBtn.isVisible()) {
  403 |       await saveBtn.click();
  404 |       
  405 |       // Should show validation error
  406 |       const titleInput = page.locator('input[id="title"]');
  407 |       const isValid = await titleInput.evaluate(el => (el as HTMLInputElement).validity.valid);
  408 |       expect(isValid).toBe(false);
  409 |     }
  410 |     
  411 |     // Test invalid time limit
  412 |     await page.fill('input[id="title"]', 'Valid Title');
  413 |     await page.fill('input[id="timeLimit"]', '-5');
  414 |     
```