# Test info

- Name: Admin Access Control >> should deny access to non-admin users
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:235:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/admin.spec.ts:241:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  141 |       await page.waitForTimeout(1000);
  142 |
  143 |       // Should show some content
  144 |       const content = page.locator('.category-management, .tags-management, .content, main');
  145 |       const contentCount = await content.count();
  146 |       expect(contentCount).toBeGreaterThan(0);
  147 |     } else {
  148 |       console.log('Category management not found - feature may not be implemented yet');
  149 |     }
  150 |   });
  151 |
  152 |   test('should backup and restore data', async ({ page }) => {
  153 |     // Look for backup section
  154 |     const backupLinks = page.locator('a:has-text("Backup"), a:has-text("Export"), a[href*="backup"]');
  155 |     const backupCount = await backupLinks.count();
  156 |
  157 |     if (backupCount > 0) {
  158 |       await backupLinks.first().click();
  159 |       await page.waitForTimeout(1000);
  160 |
  161 |       // Should show some content
  162 |       const content = page.locator('.backup-section, .export-section, .content, main');
  163 |       const contentCount = await content.count();
  164 |       expect(contentCount).toBeGreaterThan(0);
  165 |     } else {
  166 |       console.log('Backup functionality not found - feature may not be implemented yet');
  167 |     }
  168 |   });
  169 |
  170 |   test('should monitor system health', async ({ page }) => {
  171 |     // Look for system health section
  172 |     const healthLinks = page.locator('a:has-text("Health"), a:has-text("Status"), a[href*="health"]');
  173 |     const healthCount = await healthLinks.count();
  174 |
  175 |     if (healthCount > 0) {
  176 |       await healthLinks.first().click();
  177 |       await page.waitForTimeout(1000);
  178 |
  179 |       // Should show some content
  180 |       const content = page.locator('.system-health, .status-dashboard, .content, main');
  181 |       const contentCount = await content.count();
  182 |       expect(contentCount).toBeGreaterThan(0);
  183 |     } else {
  184 |       console.log('System health monitoring not found - feature may not be implemented yet');
  185 |     }
  186 |   });
  187 |
  188 |   test('should manage permissions and roles', async ({ page }) => {
  189 |     // Look for permissions section
  190 |     const permissionLinks = page.locator('a:has-text("Permissions"), a:has-text("Roles"), a[href*="permissions"]');
  191 |     const permissionCount = await permissionLinks.count();
  192 |
  193 |     if (permissionCount > 0) {
  194 |       await permissionLinks.first().click();
  195 |       await page.waitForTimeout(1000);
  196 |
  197 |       // Should show some content
  198 |       const content = page.locator('.permissions-management, .roles-management, .content, main');
  199 |       const contentCount = await content.count();
  200 |       expect(contentCount).toBeGreaterThan(0);
  201 |     } else {
  202 |       console.log('Permissions management not found - feature may not be implemented yet');
  203 |     }
  204 |   });
  205 |
  206 |   test('should handle bulk operations', async ({ page }) => {
  207 |     // Navigate to user management for bulk operations
  208 |     await page.goto('/dashboard/admin/users');
  209 |
  210 |     // Look for checkboxes to select multiple items
  211 |     const checkboxes = page.locator('input[type="checkbox"]');
  212 |     if (await checkboxes.count() > 2) {
  213 |       // Select multiple items
  214 |       await checkboxes.nth(1).check();
  215 |       await checkboxes.nth(2).check();
  216 |
  217 |       // Look for bulk action dropdown
  218 |       const bulkActionSelect = page.locator('select[name*="bulk"], .bulk-actions select');
  219 |       if (await bulkActionSelect.isVisible()) {
  220 |         await bulkActionSelect.selectOption('delete');
  221 |
  222 |         const confirmBtn = page.locator('button:has-text("Apply"), button:has-text("Execute")');
  223 |         if (await confirmBtn.isVisible()) {
  224 |           await confirmBtn.click();
  225 |
  226 |           // Should show confirmation dialog
  227 |           await expect(page.locator('.confirmation-dialog, .modal')).toBeVisible();
  228 |         }
  229 |       }
  230 |     }
  231 |   });
  232 | });
  233 |
  234 | test.describe('Admin Access Control', () => {
  235 |   test('should deny access to non-admin users', async ({ page }) => {
  236 |     // Login as regular user
  237 |     await page.goto('/auth/login');
  238 |     await page.fill('input[id="email"]', '<EMAIL>');
  239 |     await page.fill('input[id="password"]', 'user123');
  240 |     await page.click('button[type="submit"]');
> 241 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  242 |
  243 |     // Try to access admin dashboard
  244 |     await page.goto('/dashboard/admin');
  245 |
  246 |     // Should be redirected or show access denied
  247 |     const currentUrl = page.url();
  248 |     const isAccessDenied = currentUrl.includes('/dashboard') && !currentUrl.includes('/admin');
  249 |     const hasAccessDeniedMessage = await page.locator('text=Access denied, text=Unauthorized').isVisible();
  250 |
  251 |     expect(isAccessDenied || hasAccessDeniedMessage).toBe(true);
  252 |   });
  253 | });
  254 |
```