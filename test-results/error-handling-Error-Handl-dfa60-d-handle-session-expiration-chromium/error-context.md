# Test info

- Name: <PERSON>rror Handling >> should handle session expiration
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:72:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/error-handling.spec.ts:78:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Error Handling', () => {
   4 |   test('should handle 404 errors gracefully', async ({ page }) => {
   5 |     // Navigate to non-existent page
   6 |     await page.goto('/nonexistent-page');
   7 |     
   8 |     // Should show 404 page
   9 |     const notFoundElements = page.locator('h1:has-text("404"), h1:has-text("Not Found"), .not-found');
   10 |     await expect(notFoundElements.first()).toBeVisible();
   11 |     
   12 |     // Should have navigation back to home
   13 |     const homeLink = page.locator('a[href="/"], a:has-text("Home"), a:has-text("Back")');
   14 |     if (await homeLink.isVisible()) {
   15 |       await expect(homeLink).toBeVisible();
   16 |     }
   17 |   });
   18 |
   19 |   test('should handle network errors', async ({ page }) => {
   20 |     // Simulate network failure
   21 |     await page.route('**/api/**', route => route.abort());
   22 |     
   23 |     await page.goto('/auth/login');
   24 |     await page.fill('input[id="email"]', '<EMAIL>');
   25 |     await page.fill('input[id="password"]', 'user123');
   26 |     await page.click('button[type="submit"]');
   27 |     
   28 |     // Should show error message
   29 |     const errorMessage = page.locator('.error-message, .alert-error, text=error, text=failed');
   30 |     if (await errorMessage.count() > 0) {
   31 |       await expect(errorMessage.first()).toBeVisible();
   32 |     }
   33 |   });
   34 |
   35 |   test('should handle server errors (500)', async ({ page }) => {
   36 |     // Simulate server error
   37 |     await page.route('**/api/**', route => {
   38 |       route.fulfill({
   39 |         status: 500,
   40 |         contentType: 'application/json',
   41 |         body: JSON.stringify({ error: 'Internal Server Error' })
   42 |       });
   43 |     });
   44 |     
   45 |     await page.goto('/auth/login');
   46 |     await page.fill('input[id="email"]', '<EMAIL>');
   47 |     await page.fill('input[id="password"]', 'user123');
   48 |     await page.click('button[type="submit"]');
   49 |     
   50 |     // Should handle server error gracefully
   51 |     const errorIndicator = page.locator('.error, .alert, text=error, text=something went wrong');
   52 |     if (await errorIndicator.count() > 0) {
   53 |       await expect(errorIndicator.first()).toBeVisible();
   54 |     }
   55 |   });
   56 |
   57 |   test('should handle authentication errors', async ({ page }) => {
   58 |     await page.goto('/auth/login');
   59 |     
   60 |     // Try invalid credentials
   61 |     await page.fill('input[id="email"]', '<EMAIL>');
   62 |     await page.fill('input[id="password"]', 'wrongpassword');
   63 |     await page.click('button[type="submit"]');
   64 |     
   65 |     // Should show authentication error
   66 |     await expect(page.locator('text=Invalid email or password')).toBeVisible();
   67 |     
   68 |     // Should stay on login page
   69 |     expect(page.url()).toContain('/auth/login');
   70 |   });
   71 |
   72 |   test('should handle session expiration', async ({ page }) => {
   73 |     // Login first
   74 |     await page.goto('/auth/login');
   75 |     await page.fill('input[id="email"]', '<EMAIL>');
   76 |     await page.fill('input[id="password"]', 'user123');
   77 |     await page.click('button[type="submit"]');
>  78 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   79 |     
   80 |     // Clear session cookies to simulate expiration
   81 |     await page.context().clearCookies();
   82 |     
   83 |     // Try to access protected resource
   84 |     await page.goto('/dashboard/quizzes');
   85 |     
   86 |     // Should redirect to login
   87 |     await page.waitForURL('/auth/login');
   88 |     expect(page.url()).toContain('/auth/login');
   89 |   });
   90 |
   91 |   test('should handle form validation errors', async ({ page }) => {
   92 |     await page.goto('/auth/register');
   93 |     
   94 |     // Submit empty form
   95 |     await page.click('button[type="submit"]');
   96 |     
   97 |     // Should show validation errors
   98 |     const validationErrors = page.locator('.error-message, [aria-invalid="true"], .field-error');
   99 |     if (await validationErrors.count() > 0) {
  100 |       await expect(validationErrors.first()).toBeVisible();
  101 |     }
  102 |     
  103 |     // Test password mismatch
  104 |     await page.fill('input[id="name"]', 'Test User');
  105 |     await page.fill('input[id="email"]', '<EMAIL>');
  106 |     await page.fill('input[id="password"]', 'password123');
  107 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
  108 |     await page.click('button[type="submit"]');
  109 |     
  110 |     // Should show password mismatch error
  111 |     const passwordError = page.locator('text=Passwords do not match, text=password, .password-error');
  112 |     if (await passwordError.count() > 0) {
  113 |       await expect(passwordError.first()).toBeVisible();
  114 |     }
  115 |   });
  116 |
  117 |   test('should handle quiz loading errors', async ({ page }) => {
  118 |     // Login first
  119 |     await page.goto('/auth/login');
  120 |     await page.fill('input[id="email"]', '<EMAIL>');
  121 |     await page.fill('input[id="password"]', 'user123');
  122 |     await page.click('button[type="submit"]');
  123 |     await page.waitForURL('/dashboard');
  124 |     
  125 |     // Simulate quiz loading error
  126 |     await page.route('**/api/quizzes/**', route => route.abort());
  127 |     
  128 |     await page.goto('/security-quizzes');
  129 |     
  130 |     // Should show error state
  131 |     const errorState = page.locator('.error-state, .loading-error, text=failed to load, text=error loading');
  132 |     if (await errorState.count() > 0) {
  133 |       await expect(errorState.first()).toBeVisible();
  134 |     }
  135 |   });
  136 |
  137 |   test('should handle file upload errors', async ({ page }) => {
  138 |     // Login as admin
  139 |     await page.goto('/auth/login');
  140 |     await page.fill('input[id="email"]', '<EMAIL>');
  141 |     await page.fill('input[id="password"]', 'admin123');
  142 |     await page.click('button[type="submit"]');
  143 |     await page.waitForURL('/dashboard');
  144 |     
  145 |     // Navigate to quiz creation
  146 |     await page.goto('/dashboard/quizzes/create');
  147 |     
  148 |     // Look for file upload input
  149 |     const fileInput = page.locator('input[type="file"]');
  150 |     if (await fileInput.isVisible()) {
  151 |       // Simulate upload error
  152 |       await page.route('**/api/upload/**', route => {
  153 |         route.fulfill({
  154 |           status: 413,
  155 |           contentType: 'application/json',
  156 |           body: JSON.stringify({ error: 'File too large' })
  157 |         });
  158 |       });
  159 |       
  160 |       // Try to upload a file
  161 |       await fileInput.setInputFiles({
  162 |         name: 'test.txt',
  163 |         mimeType: 'text/plain',
  164 |         buffer: Buffer.from('test content')
  165 |       });
  166 |       
  167 |       // Should show upload error
  168 |       const uploadError = page.locator('.upload-error, .file-error, text=file too large, text=upload failed');
  169 |       if (await uploadError.count() > 0) {
  170 |         await expect(uploadError.first()).toBeVisible();
  171 |       }
  172 |     }
  173 |   });
  174 |
  175 |   test('should handle database connection errors', async ({ page }) => {
  176 |     // Simulate database error
  177 |     await page.route('**/api/**', route => {
  178 |       route.fulfill({
```