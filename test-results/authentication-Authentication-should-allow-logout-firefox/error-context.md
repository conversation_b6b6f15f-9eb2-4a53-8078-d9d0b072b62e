# Test info

- Name: Authentication >> should allow logout
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:115:7

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('button:has-text("Logout")')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/authentication.spec.ts:125:16
```

# Page snapshot

```yaml
- alert
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Dashboard" [level=1]
  - heading "Welcome, QuizFlow Admin!" [level=3]
  - paragraph: Manage quizzes and view your progress
  - link "Create New Quiz":
    - /url: /dashboard/quizzes/create
  - link "View All Quizzes":
    - /url: /dashboard/quizzes
  - link "Explore Quizzes":
    - /url: /explore
  - heading "Your Stats" [level=3]
  - paragraph: Your quiz activity and performance
  - paragraph: Created Quizzes
  - paragraph: "5"
  - paragraph: Completed Quizzes
  - paragraph: "0"
  - heading "Your Quizzes" [level=3]
  - paragraph: Recently created and updated quizzes
  - list:
    - listitem:
      - paragraph: Responsive Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf9260d5a800e98287f2
    - listitem:
      - paragraph: Empty Quiz Preview
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf9160d5a800e98287f1
    - listitem:
      - paragraph: Settings Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8f60d5a800e98287f0
    - listitem:
      - paragraph: Exit Preview Test
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8e60d5a800e98287ef
    - listitem:
      - paragraph: Timed Preview Quiz
      - paragraph: Draft
      - link "Edit":
        - /url: /dashboard/quizzes/6835cf8c60d5a800e98287ee
  - link "View all quizzes":
    - /url: /dashboard/quizzes
  - heading "Recent Activity" [level=3]
  - paragraph: Your recent quiz attempts
  - paragraph: You haven't taken any quizzes yet.
```

# Test source

```ts
   25 |     // Submit form
   26 |     await page.click('button[type="submit"]');
   27 |
   28 |     // Should redirect to dashboard
   29 |     await page.waitForURL('/dashboard');
   30 |     expect(page.url()).toContain('/dashboard');
   31 |
   32 |     // Should show admin features
   33 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
   34 |   });
   35 |
   36 |   test('should allow user login', async ({ page }) => {
   37 |     await page.goto('/auth/login');
   38 |
   39 |     // Fill in user credentials
   40 |     await page.fill('input[id="email"]', '<EMAIL>');
   41 |     await page.fill('input[id="password"]', 'user123');
   42 |
   43 |     // Submit form
   44 |     await page.click('button[type="submit"]');
   45 |
   46 |     // Should redirect to dashboard
   47 |     await page.waitForURL('/dashboard');
   48 |     expect(page.url()).toContain('/dashboard');
   49 |
   50 |     // Should NOT show admin features
   51 |     await expect(page.locator('text=Create Quiz')).not.toBeVisible();
   52 |   });
   53 |
   54 |   test('should show error for invalid credentials', async ({ page }) => {
   55 |     await page.goto('/auth/login');
   56 |
   57 |     // Fill in invalid credentials
   58 |     await page.fill('input[id="email"]', '<EMAIL>');
   59 |     await page.fill('input[id="password"]', 'wrongpassword');
   60 |
   61 |     // Submit form
   62 |     await page.click('button[type="submit"]');
   63 |
   64 |     // Should show error message
   65 |     await expect(page.locator('text=Invalid email or password')).toBeVisible();
   66 |
   67 |     // Should stay on login page
   68 |     expect(page.url()).toContain('/auth/login');
   69 |   });
   70 |
   71 |   test('should allow user registration', async ({ page }) => {
   72 |     await page.goto('/auth/register');
   73 |
   74 |     // Fill in registration form
   75 |     await page.fill('input[id="name"]', 'Test User');
   76 |     await page.fill('input[id="email"]', `test${Date.now()}@example.com`);
   77 |     await page.fill('input[id="password"]', 'testpassword123');
   78 |     await page.fill('input[id="confirmPassword"]', 'testpassword123');
   79 |
   80 |     // Submit form
   81 |     await page.click('button[type="submit"]');
   82 |
   83 |     // Should redirect to dashboard or show success message
   84 |     await page.waitForURL('/dashboard');
   85 |     expect(page.url()).toContain('/dashboard');
   86 |   });
   87 |
   88 |   test('should validate registration form', async ({ page }) => {
   89 |     await page.goto('/auth/register');
   90 |
   91 |     // Try to submit empty form
   92 |     await page.click('button[type="submit"]');
   93 |
   94 |     // Should show validation errors
   95 |     await expect(page.locator('input[id="email"]:invalid')).toBeVisible();
   96 |     await expect(page.locator('input[id="password"]:invalid')).toBeVisible();
   97 |   });
   98 |
   99 |   test('should validate password confirmation', async ({ page }) => {
  100 |     await page.goto('/auth/register');
  101 |
  102 |     // Fill in mismatched passwords
  103 |     await page.fill('input[id="name"]', 'Test User');
  104 |     await page.fill('input[id="email"]', '<EMAIL>');
  105 |     await page.fill('input[id="password"]', 'password123');
  106 |     await page.fill('input[id="confirmPassword"]', 'differentpassword');
  107 |
  108 |     // Submit form
  109 |     await page.click('button[type="submit"]');
  110 |
  111 |     // Should show password mismatch error
  112 |     await expect(page.locator('text=Passwords do not match')).toBeVisible();
  113 |   });
  114 |
  115 |   test('should allow logout', async ({ page }) => {
  116 |     // Login first
  117 |     await page.goto('/auth/login');
  118 |     await page.fill('input[id="email"]', '<EMAIL>');
  119 |     await page.fill('input[id="password"]', 'admin123');
  120 |     await page.click('button[type="submit"]');
  121 |
  122 |     await page.waitForURL('/dashboard');
  123 |
  124 |     // Click logout button
> 125 |     await page.click('button:has-text("Logout")');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
  126 |
  127 |     // Should redirect to home page
  128 |     await page.waitForURL('/');
  129 |     expect(page.url()).toBe('http://localhost:3000/');
  130 |
  131 |     // Should not be able to access dashboard
  132 |     await page.goto('/dashboard');
  133 |     await page.waitForURL('/auth/login');
  134 |   });
  135 |
  136 |   test('should persist session across page reloads', async ({ page }) => {
  137 |     // Login
  138 |     await page.goto('/auth/login');
  139 |     await page.fill('input[id="email"]', '<EMAIL>');
  140 |     await page.fill('input[id="password"]', 'admin123');
  141 |     await page.click('button[type="submit"]');
  142 |
  143 |     await page.waitForURL('/dashboard');
  144 |
  145 |     // Reload page
  146 |     await page.reload();
  147 |
  148 |     // Should still be logged in
  149 |     expect(page.url()).toContain('/dashboard');
  150 |     await expect(page.locator('text=Create Quiz')).toBeVisible();
  151 |   });
  152 |
  153 |   test('should handle session expiration', async ({ page }) => {
  154 |     // Login
  155 |     await page.goto('/auth/login');
  156 |     await page.fill('input[id="email"]', '<EMAIL>');
  157 |     await page.fill('input[id="password"]', 'admin123');
  158 |     await page.click('button[type="submit"]');
  159 |
  160 |     await page.waitForURL('/dashboard');
  161 |
  162 |     // Simulate session expiration by clearing cookies
  163 |     await page.context().clearCookies();
  164 |
  165 |     // Try to access protected resource
  166 |     await page.goto('/dashboard/quizzes/create');
  167 |
  168 |     // Should redirect to login
  169 |     await page.waitForURL('/auth/login');
  170 |   });
  171 |
  172 |   test('should restrict admin features to admin users', async ({ page }) => {
  173 |     // Login as regular user
  174 |     await page.goto('/auth/login');
  175 |     await page.fill('input[id="email"]', '<EMAIL>');
  176 |     await page.fill('input[id="password"]', 'user123');
  177 |     await page.click('button[type="submit"]');
  178 |
  179 |     await page.waitForURL('/dashboard');
  180 |
  181 |     // Try to access quiz creation (admin only)
  182 |     await page.goto('/dashboard/quizzes/create');
  183 |
  184 |     // Should be redirected or show access denied
  185 |     await expect(page.locator('text=Access denied')).toBeVisible();
  186 |   });
  187 | });
  188 |
```