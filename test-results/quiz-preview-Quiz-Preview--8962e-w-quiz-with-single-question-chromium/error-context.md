# Test info

- Name: Quiz Preview Functionality >> should preview quiz with single question
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:243:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=SQL Injection')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=SQL Injection')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-preview.spec.ts:289:56
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Quiz Preview" [level=1]
  - paragraph: Preview how your quiz will appear to users
  - link "Back to Edit":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287ea/edit
  - heading "Single Question Preview" [level=3]
  - paragraph: Quiz with one question for preview testing
  - text: Draft 0 Questions 15 minutes 70% to pass Tags available
  - img
  - heading "Preview Mode" [level=3]
  - paragraph: This is how your quiz will appear to users. Your answers won't be saved, and no scores will be recorded.
  - heading "No Questions Added" [level=3]
  - paragraph: Add some questions to your quiz to see the preview.
  - link "Add Questions":
    - /url: /dashboard/quizzes/6835cf7f60d5a800e98287ea/edit?tab=questions
- alert
```

# Test source

```ts
  189 |             console.log('❌ /dashboard/quizzes/[id]/preview route does not exist');
  190 |           } else {
  191 |             console.log('✅ /dashboard/quizzes/[id]/preview route exists');
  192 |           }
  193 |
  194 |         } else {
  195 |           console.log('✅ Preview page loaded successfully');
  196 |
  197 |           const pageTitle = await page.title();
  198 |           console.log('Preview page title:', pageTitle);
  199 |
  200 |           const pageContent = await page.locator('body').textContent();
  201 |           console.log('Preview page content preview:', pageContent?.substring(0, 300));
  202 |
  203 |           // Look for quiz content
  204 |           const hasQuizTitle = await page.locator('text=Preview Test Quiz').isVisible();
  205 |           const hasQuizDescription = await page.locator('text=A quiz for testing preview functionality').isVisible();
  206 |
  207 |           console.log('Quiz title visible:', hasQuizTitle);
  208 |           console.log('Quiz description visible:', hasQuizDescription);
  209 |
  210 |           if (hasQuizTitle || hasQuizDescription) {
  211 |             console.log('✅ Preview shows quiz content');
  212 |           } else {
  213 |             console.log('⚠️ Preview page loaded but quiz content not visible');
  214 |           }
  215 |         }
  216 |
  217 |       } else {
  218 |         console.log('⚠️ No navigation occurred - button might not be functional');
  219 |
  220 |         // Check if it's a modal or in-page preview
  221 |         await page.waitForTimeout(1000);
  222 |
  223 |         const modalPreview = await page.locator('.modal, .overlay, .preview-modal').isVisible();
  224 |         if (modalPreview) {
  225 |           console.log('✅ Preview opened in modal');
  226 |         } else {
  227 |           console.log('❌ Preview button clicked but no visible change');
  228 |         }
  229 |       }
  230 |
  231 |     } else {
  232 |       console.log('❌ Preview Quiz button not found');
  233 |
  234 |       // Log all available buttons for debugging
  235 |       const allButtons = await page.locator('button').allTextContents();
  236 |       console.log('Available buttons:', allButtons);
  237 |     }
  238 |
  239 |     // This test always passes - it's for debugging
  240 |     expect(true).toBe(true);
  241 |   });
  242 |
  243 |   test('should preview quiz with single question', async ({ page }) => {
  244 |     // Create quiz with one question
  245 |     await page.goto('/dashboard/quizzes/create');
  246 |     await page.fill('input[id="title"]', 'Single Question Preview');
  247 |     await page.fill('textarea[id="description"]', 'Quiz with one question for preview testing');
  248 |     await page.click('button[type="submit"]');
  249 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  250 |
  251 |     // Add a question
  252 |     await page.click('button:has-text("Questions"), .questions-tab');
  253 |     await page.click('button:has-text("Add Question"), .add-question');
  254 |
  255 |     // Fill question details
  256 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  257 |     if (await questionTypeSelect.isVisible()) {
  258 |       await questionTypeSelect.selectOption('multiple_choice');
  259 |     }
  260 |
  261 |     await page.fill('textarea[id="questionText"]', 'What is the most common web vulnerability?');
  262 |     await page.fill('input[id="points"]', '2');
  263 |
  264 |     // Add options
  265 |     const optionInputs = page.locator('input[placeholder*="option"], .option-input');
  266 |     if (await optionInputs.count() > 0) {
  267 |       await optionInputs.nth(0).fill('SQL Injection');
  268 |       await optionInputs.nth(1).fill('Cross-Site Scripting (XSS)');
  269 |       await optionInputs.nth(2).fill('Cross-Site Request Forgery (CSRF)');
  270 |       await optionInputs.nth(3).fill('Buffer Overflow');
  271 |     }
  272 |
  273 |     // Mark correct answer
  274 |     const correctAnswerRadio = page.locator('input[type="radio"][name="correctOption"]');
  275 |     if (await correctAnswerRadio.count() > 0) {
  276 |       await correctAnswerRadio.first().check();
  277 |     }
  278 |
  279 |     await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
  280 |     await page.waitForTimeout(1000);
  281 |
  282 |     // Now preview the quiz
  283 |     const previewBtn = page.locator('button:has-text("Preview"), a:has-text("Preview")');
  284 |     if (await previewBtn.isVisible()) {
  285 |       await previewBtn.click();
  286 |
  287 |       // Should show quiz with question
  288 |       await expect(page.locator('text=What is the most common web vulnerability?')).toBeVisible();
> 289 |       await expect(page.locator('text=SQL Injection')).toBeVisible();
      |                                                        ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  290 |       await expect(page.locator('text=Cross-Site Scripting')).toBeVisible();
  291 |     }
  292 |   });
  293 |
  294 |   test('should preview quiz with multiple question types', async ({ page }) => {
  295 |     // Create quiz with multiple question types
  296 |     await page.goto('/dashboard/quizzes/create');
  297 |     await page.fill('input[id="title"]', 'Multi-Type Question Preview');
  298 |     await page.fill('textarea[id="description"]', 'Quiz with different question types');
  299 |     await page.click('button[type="submit"]');
  300 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  301 |
  302 |     // Add multiple choice question
  303 |     await page.click('button:has-text("Questions")');
  304 |     await page.click('button:has-text("Add Question")');
  305 |
  306 |     const questionTypeSelect = page.locator('select[id="questionType"]');
  307 |     if (await questionTypeSelect.isVisible()) {
  308 |       await questionTypeSelect.selectOption('multiple_choice');
  309 |       await page.fill('textarea[id="questionText"]', 'Which protocol is used for secure web communication?');
  310 |
  311 |       const optionInputs = page.locator('input[placeholder*="option"]');
  312 |       if (await optionInputs.count() >= 2) {
  313 |         await optionInputs.nth(0).fill('HTTP');
  314 |         await optionInputs.nth(1).fill('HTTPS');
  315 |       }
  316 |
  317 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  318 |       await page.waitForTimeout(1000);
  319 |
  320 |       // Add true/false question
  321 |       await page.click('button:has-text("Add Question")');
  322 |       await questionTypeSelect.selectOption('true_false');
  323 |       await page.fill('textarea[id="questionText"]', 'HTTPS encrypts all data transmission.');
  324 |
  325 |       const trueFalseOptions = page.locator('input[value="true"], input[value="false"]');
  326 |       if (await trueFalseOptions.count() > 0) {
  327 |         await trueFalseOptions.first().check();
  328 |       }
  329 |
  330 |       await page.click('button:has-text("Add Question"), button:has-text("Save")');
  331 |       await page.waitForTimeout(1000);
  332 |     }
  333 |
  334 |     // Preview the quiz
  335 |     const previewBtn = page.locator('button:has-text("Preview")');
  336 |     if (await previewBtn.isVisible()) {
  337 |       await previewBtn.click();
  338 |
  339 |       // Should show both questions
  340 |       await expect(page.locator('text=Which protocol is used for secure web communication?')).toBeVisible();
  341 |       await expect(page.locator('text=HTTPS encrypts all data transmission')).toBeVisible();
  342 |     }
  343 |   });
  344 |
  345 |   test('should preview quiz navigation', async ({ page }) => {
  346 |     // Create quiz with multiple questions for navigation testing
  347 |     await page.goto('/dashboard/quizzes/create');
  348 |     await page.fill('input[id="title"]', 'Navigation Preview Test');
  349 |     await page.fill('textarea[id="description"]', 'Testing navigation in preview mode');
  350 |     await page.click('button[type="submit"]');
  351 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  352 |
  353 |     // Add two questions quickly
  354 |     await page.click('button:has-text("Questions")');
  355 |
  356 |     for (let i = 1; i <= 2; i++) {
  357 |       await page.click('button:has-text("Add Question")');
  358 |
  359 |       const questionTypeSelect = page.locator('select[id="questionType"]');
  360 |       if (await questionTypeSelect.isVisible()) {
  361 |         await questionTypeSelect.selectOption('multiple_choice');
  362 |         await page.fill('textarea[id="questionText"]', `Question ${i}: Test question for navigation`);
  363 |
  364 |         const optionInputs = page.locator('input[placeholder*="option"]');
  365 |         if (await optionInputs.count() >= 2) {
  366 |           await optionInputs.nth(0).fill(`Option A for Q${i}`);
  367 |           await optionInputs.nth(1).fill(`Option B for Q${i}`);
  368 |         }
  369 |
  370 |         await page.click('button:has-text("Add Question"), button:has-text("Save")');
  371 |         await page.waitForTimeout(1000);
  372 |       }
  373 |     }
  374 |
  375 |     // Preview the quiz
  376 |     const previewBtn = page.locator('button:has-text("Preview")');
  377 |     if (await previewBtn.isVisible()) {
  378 |       await previewBtn.click();
  379 |
  380 |       // Should show first question
  381 |       await expect(page.locator('text=Question 1')).toBeVisible();
  382 |
  383 |       // Test navigation
  384 |       const nextBtn = page.locator('button:has-text("Next"), .next-question');
  385 |       if (await nextBtn.isVisible()) {
  386 |         await nextBtn.click();
  387 |         await expect(page.locator('text=Question 2')).toBeVisible();
  388 |
  389 |         // Test previous navigation
```