# Test info

- Name: Quiz Editing - All Possibilities >> should edit quiz with different question types
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:281:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editing.spec.ts:10:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": admin123
- button "Logging in..." [disabled]
- text: Or continue with
- button "Google" [disabled]
- button "GitHub" [disabled]
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Quiz Editing - All Possibilities', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login as admin user
   6 |     await page.goto('/auth/login');
   7 |     await page.fill('input[id="email"]', '<EMAIL>');
   8 |     await page.fill('input[id="password"]', 'admin123');
   9 |     await page.click('button[type="submit"]');
>  10 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   11 |   });
   12 |
   13 |   test('should edit basic quiz information', async ({ page }) => {
   14 |     // Create a quiz first
   15 |     await page.goto('/dashboard/quizzes/create');
   16 |     await page.fill('input[id="title"]', 'Original Quiz Title');
   17 |     await page.fill('textarea[id="description"]', 'Original description');
   18 |     await page.click('button[type="submit"]');
   19 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
   20 |
   21 |     // Edit the quiz information
   22 |     await page.fill('input[id="title"]', 'Updated Quiz Title');
   23 |     await page.fill('textarea[id="description"]', 'Updated description with more details');
   24 |
   25 |     // Save changes (auto-save or manual save)
   26 |     const saveBtn = page.locator('button:has-text("Save"), button:has-text("Update")');
   27 |     if (await saveBtn.isVisible()) {
   28 |       await saveBtn.click();
   29 |       await page.waitForTimeout(1000);
   30 |     }
   31 |
   32 |     // Refresh page to verify changes were saved
   33 |     await page.reload();
   34 |     await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
   35 |     await expect(page.locator('textarea[id="description"]')).toHaveValue('Updated description with more details');
   36 |   });
   37 |
   38 |   test('should edit quiz settings', async ({ page }) => {
   39 |     // Create a quiz with initial settings
   40 |     await page.goto('/dashboard/quizzes/create');
   41 |     await page.fill('input[id="title"]', 'Settings Edit Test');
   42 |     await page.fill('textarea[id="description"]', 'Testing settings editing');
   43 |     await page.fill('input[id="passingScore"]', '70');
   44 |     await page.fill('input[id="timeLimit"]', '20');
   45 |     await page.click('button[type="submit"]');
   46 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
   47 |
   48 |     // Edit the settings
   49 |     await page.fill('input[id="passingScore"]', '85');
   50 |     await page.fill('input[id="timeLimit"]', '45');
   51 |     await page.fill('input[id="tags"]', 'updated, settings, test');
   52 |
   53 |     // Save changes
   54 |     const saveBtn = page.locator('button:has-text("Save")');
   55 |     if (await saveBtn.isVisible()) {
   56 |       await saveBtn.click();
   57 |       await page.waitForTimeout(1000);
   58 |     }
   59 |
   60 |     // Verify changes
   61 |     await page.reload();
   62 |     await expect(page.locator('input[id="passingScore"]')).toHaveValue('85');
   63 |     await expect(page.locator('input[id="timeLimit"]')).toHaveValue('45');
   64 |     await expect(page.locator('input[id="tags"]')).toHaveValue('updated, settings, test');
   65 |   });
   66 |
   67 |   test('should add questions to existing quiz', async ({ page }) => {
   68 |     // Create a quiz without questions
   69 |     await page.goto('/dashboard/quizzes/create');
   70 |     await page.fill('input[id="title"]', 'Question Addition Test');
   71 |     await page.fill('textarea[id="description"]', 'Testing adding questions');
   72 |     await page.click('button[type="submit"]');
   73 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
   74 |
   75 |     // Navigate to questions section
   76 |     await page.click('button:has-text("Questions"), .questions-tab');
   77 |
   78 |     // Add first question
   79 |     await page.click('button:has-text("Add Question")');
   80 |
   81 |     const questionTypeSelect = page.locator('select[id="questionType"]');
   82 |     if (await questionTypeSelect.isVisible()) {
   83 |       await questionTypeSelect.selectOption('multiple_choice');
   84 |       await page.fill('textarea[id="questionText"]', 'What is SQL injection?');
   85 |       await page.fill('input[id="points"]', '3');
   86 |
   87 |       // Add options
   88 |       const optionInputs = page.locator('input[placeholder*="option"]');
   89 |       if (await optionInputs.count() >= 4) {
   90 |         await optionInputs.nth(0).fill('A database query attack');
   91 |         await optionInputs.nth(1).fill('A network protocol');
   92 |         await optionInputs.nth(2).fill('A programming language');
   93 |         await optionInputs.nth(3).fill('An encryption method');
   94 |       }
   95 |
   96 |       // Mark correct answer
   97 |       const correctAnswerRadio = page.locator('input[type="radio"][name="correctOption"]');
   98 |       if (await correctAnswerRadio.count() > 0) {
   99 |         await correctAnswerRadio.first().check();
  100 |       }
  101 |
  102 |       await page.click('button:has-text("Add Question"), button:has-text("Save Question")');
  103 |       await page.waitForTimeout(1000);
  104 |     }
  105 |
  106 |     // Add second question of different type
  107 |     await page.click('button:has-text("Add Question")');
  108 |
  109 |     if (await questionTypeSelect.isVisible()) {
  110 |       await questionTypeSelect.selectOption('true_false');
```