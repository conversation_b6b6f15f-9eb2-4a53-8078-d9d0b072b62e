# Test info

- Name: Quiz Editor >> should handle different question types
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editor.spec.ts:172:7

# Error details

```
Error: page.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('input[value="true"]')

    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-editor.spec.ts:186:16
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Edit Quiz" [level=1]
  - button "Back to Quizzes"
  - button "Preview Quiz"
  - tablist:
    - tab "Quiz Details"
    - tab "Questions" [selected]
    - tab "Question Pools"
    - tab "Publish"
  - tabpanel "Questions":
    - heading "Questions" [level=3]
    - paragraph: Add, edit, or remove questions from your quiz
    - tablist:
      - tab "Existing Questions"
      - tab "Add Question" [selected]
    - tabpanel "Add Question":
      - text: Question Type
      - combobox "Question Type":
        - option "Multiple Choice"
        - option "True/False" [selected]
        - option "Short Answer"
        - option "Matching"
        - option "Fill in the Blank"
        - option "Essay"
      - heading "Add New Question" [level=3]
      - text: Question Text
      - textbox "Question Text": Is this a true/false question?
      - text: Points
      - spinbutton "Points": "1"
      - text: Correct Answer
      - radio "True" [checked]
      - text: "True"
      - radio "False"
      - text: False Feedback for Correct Answer
      - textbox "Feedback for Correct Answer"
      - text: Feedback for Incorrect Answer
      - textbox "Feedback for Incorrect Answer"
      - button "Add Question"
- alert
```

# Test source

```ts
   86 |     // Click on the first quiz
   87 |     await page.click('.quiz-card:first-child a');
   88 |
   89 |     // Navigate to Questions tab
   90 |     await page.click('button:has-text("Questions")');
   91 |
   92 |     // Click on first question to edit
   93 |     await page.click('.question-item:first-child');
   94 |
   95 |     // Modify question text
   96 |     const questionTextarea = page.locator('textarea[id="questionText"]');
   97 |     await questionTextarea.clear();
   98 |     await questionTextarea.fill('Updated question text');
   99 |
  100 |     // Save changes
  101 |     await page.click('button:has-text("Update Question")');
  102 |
  103 |     // Verify update was successful
  104 |     await page.waitForSelector('text=Question updated successfully', { timeout: 10000 });
  105 |   });
  106 |
  107 |   test('should delete a question', async ({ page }) => {
  108 |     // Navigate to a quiz with existing questions
  109 |     await page.goto('/dashboard/quizzes');
  110 |
  111 |     // Click on the first quiz
  112 |     await page.click('.quiz-card:first-child a');
  113 |
  114 |     // Navigate to Questions tab
  115 |     await page.click('button:has-text("Questions")');
  116 |
  117 |     // Get initial question count
  118 |     const initialQuestions = await page.locator('.question-item').count();
  119 |
  120 |     // Click delete button on first question
  121 |     await page.click('.question-item:first-child button[aria-label="Delete"]');
  122 |
  123 |     // Confirm deletion in dialog
  124 |     page.on('dialog', dialog => dialog.accept());
  125 |
  126 |     // Verify question was deleted
  127 |     await page.waitForFunction(
  128 |       (expectedCount) => document.querySelectorAll('.question-item').length === expectedCount,
  129 |       initialQuestions - 1
  130 |     );
  131 |   });
  132 |
  133 |   test('should validate required fields', async ({ page }) => {
  134 |     // Create a quiz first
  135 |     await page.goto('/dashboard/quizzes/create');
  136 |     await page.fill('input[id="title"]', 'Validation Test Quiz');
  137 |     await page.fill('textarea[id="description"]', 'Testing validation');
  138 |     await page.click('button:has-text("Create Quiz")');
  139 |
  140 |     // Wait for redirect to editor
  141 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  142 |
  143 |     // Navigate to Questions tab
  144 |     await page.click('button:has-text("Questions")');
  145 |     await page.click('button:has-text("Add Question")');
  146 |
  147 |     // Try to save without filling required fields
  148 |     await page.click('button:has-text("Add Question")');
  149 |
  150 |     // Should show validation errors
  151 |     await expect(page.locator('textarea[id="questionText"]:invalid')).toBeVisible();
  152 |   });
  153 |
  154 |   test('should preview quiz', async ({ page }) => {
  155 |     // Navigate to a quiz
  156 |     await page.goto('/dashboard/quizzes');
  157 |     await page.click('.quiz-card:first-child a');
  158 |
  159 |     // Click preview button
  160 |     await page.click('button:has-text("Preview Quiz")');
  161 |
  162 |     // Should open preview in new tab/window
  163 |     const [previewPage] = await Promise.all([
  164 |       page.waitForEvent('popup'),
  165 |       page.click('button:has-text("Preview Quiz")')
  166 |     ]);
  167 |
  168 |     // Verify preview page loaded
  169 |     await expect(previewPage.locator('h1')).toBeVisible();
  170 |   });
  171 |
  172 |   test('should handle different question types', async ({ page }) => {
  173 |     // Create a quiz first
  174 |     await page.goto('/dashboard/quizzes/create');
  175 |     await page.fill('input[id="title"]', 'Question Types Test');
  176 |     await page.fill('textarea[id="description"]', 'Testing different question types');
  177 |     await page.click('button:has-text("Create Quiz")');
  178 |
  179 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  180 |     await page.click('button:has-text("Questions")');
  181 |     await page.click('button:has-text("Add Question")');
  182 |
  183 |     // Test True/False question
  184 |     await page.selectOption('select[id="questionType"]', 'true_false');
  185 |     await page.fill('textarea[id="questionText"]', 'Is this a true/false question?');
> 186 |     await page.click('input[value="true"]');
      |                ^ Error: page.click: Test timeout of 30000ms exceeded.
  187 |     await page.click('button:has-text("Add Question")');
  188 |
  189 |     await page.waitForSelector('text=Question added successfully');
  190 |
  191 |     // Test Short Answer question
  192 |     await page.selectOption('select[id="questionType"]', 'short_answer');
  193 |     await page.fill('textarea[id="questionText"]', 'What is your name?');
  194 |     await page.fill('input[placeholder="Enter correct answer"]', 'John Doe');
  195 |     await page.click('button:has-text("Add Question")');
  196 |
  197 |     await page.waitForSelector('text=Question added successfully');
  198 |   });
  199 |
  200 |   test('should save quiz progress automatically', async ({ page }) => {
  201 |     // Navigate to a quiz with questions
  202 |     await page.goto('/dashboard/quizzes');
  203 |     await page.click('.quiz-card:first-child a');
  204 |
  205 |     // Make changes to quiz metadata
  206 |     await page.fill('input[id="title"]', 'Updated Quiz Title');
  207 |
  208 |     // Wait for auto-save indicator
  209 |     await page.waitForSelector('text=Saved', { timeout: 5000 });
  210 |
  211 |     // Refresh page to verify changes were saved
  212 |     await page.reload();
  213 |     await expect(page.locator('input[id="title"]')).toHaveValue('Updated Quiz Title');
  214 |   });
  215 |
  216 |   test('should handle quiz publishing', async ({ page }) => {
  217 |     // Navigate to a quiz
  218 |     await page.goto('/dashboard/quizzes');
  219 |     await page.click('.quiz-card:first-child a');
  220 |
  221 |     // Publish the quiz
  222 |     await page.click('button:has-text("Publish Quiz")');
  223 |
  224 |     // Confirm publishing
  225 |     await page.click('button:has-text("Confirm")');
  226 |
  227 |     // Should show published status
  228 |     await expect(page.locator('text=Published')).toBeVisible();
  229 |   });
  230 | });
  231 |
```