# Test info

- Name: Quiz Creation - All Possibilities >> should validate required fields
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-creation.spec.ts:192:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: false
Received: true
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/quiz-creation.spec.ts:206:44
```

# Page snapshot

```yaml
- banner:
  - link "QuizFlow":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "My Quizzes":
      - /url: /dashboard/quizzes
    - link "Activity":
      - /url: /dashboard/activity
    - link "Explore":
      - /url: /explore
    - link "Admin":
      - /url: /dashboard/admin
  - text: <NAME_EMAIL>
  - button "Sign Out"
- main:
  - heading "Create New Quiz" [level=1]
  - heading "Quiz Details" [level=3]
  - paragraph: Enter the basic information for your new quiz
  - text: Quiz Title
  - textbox "Quiz Title"
  - text: Description
  - textbox "Description"
  - text: Tags
  - textbox "Tags"
  - text: Passing Score (%)
  - spinbutton "Passing Score (%)": "70"
  - text: Time Limit (minutes)
  - spinbutton "Time Limit (minutes)": "15"
  - button "Cancel"
  - button "Create Quiz"
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
  106 |       await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  107 |       
  108 |       // Verify quiz was created
  109 |       await expect(page.locator('input[id="title"]')).toHaveValue(`${category} Fundamentals`);
  110 |       
  111 |       // Go back to create another
  112 |       if (category !== 'Malware Analysis') {
  113 |         await page.goto('/dashboard/quizzes/create');
  114 |       }
  115 |     }
  116 |   });
  117 |
  118 |   test('should create quiz with time limits', async ({ page }) => {
  119 |     const timeLimits = ['10', '30', '60', '120'];
  120 |     
  121 |     for (const timeLimit of timeLimits) {
  122 |       await page.goto('/dashboard/quizzes/create');
  123 |       
  124 |       await page.fill('input[id="title"]', `${timeLimit} Minute Security Challenge`);
  125 |       await page.fill('textarea[id="description"]', `A timed security quiz with ${timeLimit} minute limit`);
  126 |       await page.fill('input[id="timeLimit"]', timeLimit);
  127 |       
  128 |       await page.click('button[type="submit"]');
  129 |       await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  130 |       
  131 |       // Verify time limit was saved
  132 |       await expect(page.locator('input[id="timeLimit"]')).toHaveValue(timeLimit);
  133 |       
  134 |       // Go back to create another
  135 |       if (timeLimit !== '120') {
  136 |         await page.goto('/dashboard/quizzes/create');
  137 |       }
  138 |     }
  139 |   });
  140 |
  141 |   test('should create quiz with different passing scores', async ({ page }) => {
  142 |     const passingScores = ['60', '70', '80', '90'];
  143 |     
  144 |     for (const score of passingScores) {
  145 |       await page.goto('/dashboard/quizzes/create');
  146 |       
  147 |       await page.fill('input[id="title"]', `High Standards Quiz (${score}% to pass)`);
  148 |       await page.fill('textarea[id="description"]', `Challenging quiz requiring ${score}% to pass`);
  149 |       await page.fill('input[id="passingScore"]', score);
  150 |       
  151 |       await page.click('button[type="submit"]');
  152 |       await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  153 |       
  154 |       // Verify passing score was saved
  155 |       await expect(page.locator('input[id="passingScore"]')).toHaveValue(score);
  156 |       
  157 |       // Go back to create another
  158 |       if (score !== '90') {
  159 |         await page.goto('/dashboard/quizzes/create');
  160 |       }
  161 |     }
  162 |   });
  163 |
  164 |   test('should create quiz with multiple tags', async ({ page }) => {
  165 |     const tagCombinations = [
  166 |       'security, basics, fundamentals',
  167 |       'advanced, penetration testing, red team',
  168 |       'blue team, defense, monitoring, SIEM',
  169 |       'compliance, governance, risk management'
  170 |     ];
  171 |     
  172 |     for (let i = 0; i < tagCombinations.length; i++) {
  173 |       await page.goto('/dashboard/quizzes/create');
  174 |       
  175 |       await page.fill('input[id="title"]', `Tagged Quiz ${i + 1}`);
  176 |       await page.fill('textarea[id="description"]', `Quiz with multiple relevant tags`);
  177 |       await page.fill('input[id="tags"]', tagCombinations[i]);
  178 |       
  179 |       await page.click('button[type="submit"]');
  180 |       await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  181 |       
  182 |       // Verify tags were saved
  183 |       await expect(page.locator('input[id="tags"]')).toHaveValue(tagCombinations[i]);
  184 |       
  185 |       // Go back to create another
  186 |       if (i < tagCombinations.length - 1) {
  187 |         await page.goto('/dashboard/quizzes/create');
  188 |       }
  189 |     }
  190 |   });
  191 |
  192 |   test('should validate required fields', async ({ page }) => {
  193 |     await page.goto('/dashboard/quizzes/create');
  194 |     
  195 |     // Try to submit without filling required fields
  196 |     await page.click('button[type="submit"]');
  197 |     
  198 |     // Should show validation errors
  199 |     const titleInput = page.locator('input[id="title"]');
  200 |     const descriptionInput = page.locator('textarea[id="description"]');
  201 |     
  202 |     // Check for HTML5 validation or custom validation messages
  203 |     const titleValid = await titleInput.evaluate(el => (el as HTMLInputElement).validity.valid);
  204 |     const descriptionValid = await descriptionInput.evaluate(el => (el as HTMLTextAreaElement).validity.valid);
  205 |     
> 206 |     expect(titleValid || descriptionValid).toBe(false); // At least one should be invalid
      |                                            ^ Error: expect(received).toBe(expected) // Object.is equality
  207 |     
  208 |     // Should stay on create page
  209 |     expect(page.url()).toContain('/create');
  210 |   });
  211 |
  212 |   test('should handle special characters in quiz data', async ({ page }) => {
  213 |     await page.goto('/dashboard/quizzes/create');
  214 |     
  215 |     // Use special characters and unicode
  216 |     await page.fill('input[id="title"]', 'Quiz with Special Characters: @#$%^&*()');
  217 |     await page.fill('textarea[id="description"]', 'Description with unicode: 🔒 Security Quiz with émojis and àccénts');
  218 |     await page.fill('input[id="tags"]', 'special-chars, unicode-test, symbols@#$');
  219 |     
  220 |     await page.click('button[type="submit"]');
  221 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  222 |     
  223 |     // Verify special characters were preserved
  224 |     await expect(page.locator('input[id="title"]')).toHaveValue('Quiz with Special Characters: @#$%^&*()');
  225 |     await expect(page.locator('textarea[id="description"]')).toHaveValue('Description with unicode: 🔒 Security Quiz with émojis and àccénts');
  226 |     await expect(page.locator('input[id="tags"]')).toHaveValue('special-chars, unicode-test, symbols@#$');
  227 |   });
  228 |
  229 |   test('should create quiz with maximum field lengths', async ({ page }) => {
  230 |     await page.goto('/dashboard/quizzes/create');
  231 |     
  232 |     // Create very long content to test limits
  233 |     const longTitle = 'A'.repeat(200); // Assuming 200 char limit
  234 |     const longDescription = 'B'.repeat(1000); // Assuming 1000 char limit
  235 |     const longTags = 'tag1, tag2, tag3, tag4, tag5, tag6, tag7, tag8, tag9, tag10, very-long-tag-name-that-tests-limits';
  236 |     
  237 |     await page.fill('input[id="title"]', longTitle);
  238 |     await page.fill('textarea[id="description"]', longDescription);
  239 |     await page.fill('input[id="tags"]', longTags);
  240 |     
  241 |     await page.click('button[type="submit"]');
  242 |     await page.waitForURL(/\/dashboard\/quizzes\/.*\/edit/);
  243 |     
  244 |     // Verify content was saved (may be truncated based on limits)
  245 |     const savedTitle = await page.locator('input[id="title"]').inputValue();
  246 |     const savedDescription = await page.locator('textarea[id="description"]').inputValue();
  247 |     
  248 |     expect(savedTitle.length).toBeGreaterThan(0);
  249 |     expect(savedDescription.length).toBeGreaterThan(0);
  250 |   });
  251 | });
  252 |
```