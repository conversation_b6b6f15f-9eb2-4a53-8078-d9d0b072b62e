# Test info

- Name: Mobile Tests >> should support swipe gestures
- Location: /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:112:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /Users/<USER>/Documents/augment-projects/hacking-quiz/tests/e2e/mobile.spec.ts:117:16
```

# Page snapshot

```yaml
- heading "Login" [level=3]
- paragraph: Enter your credentials to access your account
- text: Invalid email or password Email
- textbox "Email": <EMAIL>
- text: Password
- link "Forgot password?":
  - /url: /auth/forgot-password
- textbox "Password": user123
- button "Login"
- text: Or continue with
- button "Google"
- button "GitHub"
- paragraph:
  - text: Don't have an account?
  - link "Sign up":
    - /url: /auth/register
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```

# Test source

```ts
   17 |     const formBox = await form.boundingBox();
   18 |     
   19 |     if (formBox) {
   20 |       // Form should not exceed viewport width
   21 |       expect(formBox.width).toBeLessThanOrEqual(375);
   22 |     }
   23 |     
   24 |     // Input fields should be touch-friendly
   25 |     const inputs = page.locator('input');
   26 |     const inputCount = await inputs.count();
   27 |     
   28 |     for (let i = 0; i < inputCount; i++) {
   29 |       const input = inputs.nth(i);
   30 |       const inputBox = await input.boundingBox();
   31 |       
   32 |       if (inputBox) {
   33 |         // Touch targets should be at least 44px high
   34 |         expect(inputBox.height).toBeGreaterThanOrEqual(40);
   35 |       }
   36 |     }
   37 |   });
   38 |
   39 |   test('should have mobile navigation', async ({ page }) => {
   40 |     // Login first
   41 |     await page.goto('/auth/login');
   42 |     await page.fill('input[id="email"]', '<EMAIL>');
   43 |     await page.fill('input[id="password"]', 'user123');
   44 |     await page.click('button[type="submit"]');
   45 |     await page.waitForURL('/dashboard');
   46 |     
   47 |     // Look for mobile menu button
   48 |     const mobileMenuBtn = page.locator('.mobile-menu, .hamburger, button[aria-label*="menu"], .menu-toggle');
   49 |     if (await mobileMenuBtn.isVisible()) {
   50 |       await mobileMenuBtn.click();
   51 |       
   52 |       // Should show navigation menu
   53 |       await expect(page.locator('nav, .navigation, .mobile-nav')).toBeVisible();
   54 |     }
   55 |   });
   56 |
   57 |   test('should display quiz cards responsively', async ({ page }) => {
   58 |     // Login and navigate to quizzes
   59 |     await page.goto('/auth/login');
   60 |     await page.fill('input[id="email"]', '<EMAIL>');
   61 |     await page.fill('input[id="password"]', 'user123');
   62 |     await page.click('button[type="submit"]');
   63 |     await page.waitForURL('/dashboard');
   64 |     
   65 |     await page.goto('/security-quizzes');
   66 |     
   67 |     // Should show quiz cards
   68 |     const quizCards = page.locator('.quiz-card, .quiz-item');
   69 |     await expect(quizCards).toHaveCount.greaterThan(0);
   70 |     
   71 |     // Cards should stack vertically on mobile
   72 |     const firstCard = quizCards.first();
   73 |     const secondCard = quizCards.nth(1);
   74 |     
   75 |     if (await secondCard.isVisible()) {
   76 |       const firstBox = await firstCard.boundingBox();
   77 |       const secondBox = await secondCard.boundingBox();
   78 |       
   79 |       if (firstBox && secondBox) {
   80 |         // Second card should be below first card (stacked)
   81 |         expect(secondBox.y).toBeGreaterThan(firstBox.y + firstBox.height - 10);
   82 |       }
   83 |     }
   84 |   });
   85 |
   86 |   test('should handle touch interactions', async ({ page }) => {
   87 |     await page.goto('/auth/login');
   88 |     await page.fill('input[id="email"]', '<EMAIL>');
   89 |     await page.fill('input[id="password"]', 'user123');
   90 |     await page.click('button[type="submit"]');
   91 |     await page.waitForURL('/dashboard');
   92 |     
   93 |     await page.goto('/security-quizzes');
   94 |     
   95 |     // Test touch tap on quiz card
   96 |     const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
   97 |     if (await firstQuiz.isVisible()) {
   98 |       // Simulate touch tap
   99 |       await firstQuiz.tap();
  100 |       
  101 |       // Should navigate to quiz details or start quiz
  102 |       await page.waitForTimeout(1000);
  103 |       
  104 |       // URL should change or modal should appear
  105 |       const currentUrl = page.url();
  106 |       const modal = page.locator('.modal, .dialog');
  107 |       
  108 |       expect(currentUrl.includes('/security-quizzes') || await modal.isVisible()).toBe(true);
  109 |     }
  110 |   });
  111 |
  112 |   test('should support swipe gestures', async ({ page }) => {
  113 |     await page.goto('/auth/login');
  114 |     await page.fill('input[id="email"]', '<EMAIL>');
  115 |     await page.fill('input[id="password"]', 'user123');
  116 |     await page.click('button[type="submit"]');
> 117 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  118 |     
  119 |     await page.goto('/security-quizzes');
  120 |     
  121 |     // Look for swipeable content (carousels, etc.)
  122 |     const swipeableContent = page.locator('.carousel, .swiper, .slider');
  123 |     if (await swipeableContent.isVisible()) {
  124 |       const contentBox = await swipeableContent.boundingBox();
  125 |       
  126 |       if (contentBox) {
  127 |         // Simulate swipe gesture
  128 |         await page.mouse.move(contentBox.x + contentBox.width * 0.8, contentBox.y + contentBox.height / 2);
  129 |         await page.mouse.down();
  130 |         await page.mouse.move(contentBox.x + contentBox.width * 0.2, contentBox.y + contentBox.height / 2);
  131 |         await page.mouse.up();
  132 |         
  133 |         // Content should respond to swipe
  134 |         await page.waitForTimeout(500);
  135 |         await expect(swipeableContent).toBeVisible();
  136 |       }
  137 |     }
  138 |   });
  139 |
  140 |   test('should optimize for different mobile sizes', async ({ page }) => {
  141 |     const mobileSizes = [
  142 |       { width: 320, height: 568, name: 'iPhone 5' },
  143 |       { width: 375, height: 667, name: 'iPhone SE' },
  144 |       { width: 414, height: 896, name: 'iPhone 11' },
  145 |       { width: 360, height: 640, name: 'Android' }
  146 |     ];
  147 |     
  148 |     for (const size of mobileSizes) {
  149 |       await page.setViewportSize({ width: size.width, height: size.height });
  150 |       await page.goto('/security-quizzes');
  151 |       
  152 |       // Should display content properly
  153 |       await expect(page.locator('h1')).toBeVisible();
  154 |       
  155 |       // Content should not overflow
  156 |       const body = page.locator('body');
  157 |       const bodyBox = await body.boundingBox();
  158 |       
  159 |       if (bodyBox) {
  160 |         expect(bodyBox.width).toBeLessThanOrEqual(size.width + 20); // Allow small margin
  161 |       }
  162 |     }
  163 |   });
  164 |
  165 |   test('should handle mobile form inputs', async ({ page }) => {
  166 |     await page.goto('/auth/register');
  167 |     
  168 |     // Test mobile keyboard types
  169 |     const emailInput = page.locator('input[type="email"], input[id="email"]');
  170 |     if (await emailInput.isVisible()) {
  171 |       const inputType = await emailInput.getAttribute('type');
  172 |       const inputMode = await emailInput.getAttribute('inputmode');
  173 |       
  174 |       // Should use appropriate input type for mobile keyboards
  175 |       expect(inputType === 'email' || inputMode === 'email').toBe(true);
  176 |     }
  177 |     
  178 |     // Test form validation on mobile
  179 |     await page.click('button[type="submit"]');
  180 |     
  181 |     // Should show validation messages
  182 |     const validationMessages = page.locator('.error-message, [aria-invalid="true"]');
  183 |     if (await validationMessages.count() > 0) {
  184 |       await expect(validationMessages.first()).toBeVisible();
  185 |     }
  186 |   });
  187 |
  188 |   test('should support mobile accessibility', async ({ page }) => {
  189 |     await page.goto('/auth/login');
  190 |     
  191 |     // Test touch target sizes
  192 |     const buttons = page.locator('button');
  193 |     const buttonCount = await buttons.count();
  194 |     
  195 |     for (let i = 0; i < Math.min(buttonCount, 5); i++) {
  196 |       const button = buttons.nth(i);
  197 |       const buttonBox = await button.boundingBox();
  198 |       
  199 |       if (buttonBox) {
  200 |         // Touch targets should be at least 44x44px
  201 |         expect(buttonBox.height).toBeGreaterThanOrEqual(40);
  202 |         expect(buttonBox.width).toBeGreaterThanOrEqual(40);
  203 |       }
  204 |     }
  205 |   });
  206 |
  207 |   test('should handle mobile quiz taking', async ({ page }) => {
  208 |     // Login and start a quiz
  209 |     await page.goto('/auth/login');
  210 |     await page.fill('input[id="email"]', '<EMAIL>');
  211 |     await page.fill('input[id="password"]', 'user123');
  212 |     await page.click('button[type="submit"]');
  213 |     await page.waitForURL('/dashboard');
  214 |     
  215 |     await page.goto('/security-quizzes');
  216 |     
  217 |     const firstQuiz = page.locator('.quiz-card, .quiz-item').first();
```