"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Quiz, Question, QuestionPool, SelectionRule } from "@/generated/prisma";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import QuizDetailsForm from "./QuizDetailsForm";
import QuestionsManager from "./QuestionsManager";
import PoolsManager from "./PoolsManager";
import PublishSettings from "./PublishSettings";

type QuizWithRelations = Quiz & {
  questions: Question[];
  questionPools: (QuestionPool & {
    questions: Question[];
  })[];
  selectionRules: SelectionRule[];
};

interface QuizEditorProps {
  quiz: QuizWithRelations;
}

export default function QuizEditor({ quiz: initialQuiz }: QuizEditorProps) {
  const router = useRouter();
  const [quiz, setQuiz] = useState<QuizWithRelations>(initialQuiz);
  const [activeTab, setActiveTab] = useState("details");
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // Save quiz details
  const handleSaveQuizDetails = async (updatedDetails: Partial<Quiz>) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const response = await fetch(`/api/quizzes/${quiz.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedDetails),
      });

      if (!response.ok) {
        throw new Error("Failed to update quiz details");
      }

      const updatedQuiz = await response.json();
      setQuiz((prev) => ({ ...prev, ...updatedQuiz }));
      setSaveMessage("Quiz details saved successfully");
    } catch (error) {
      console.error("Error saving quiz details:", error);
      setSaveMessage("Error saving quiz details");
    } finally {
      setIsSaving(false);
    }
  };

  // Add a new question
  const handleAddQuestion = async (newQuestion: Omit<Question, "id" | "quizId" | "createdAt" | "updatedAt">) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/questions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newQuestion),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error("API Error Response:", errorData);
        throw new Error(`Failed to add question: ${response.status} - ${errorData}`);
      }

      const addedQuestion = await response.json();
      setQuiz((prev) => ({
        ...prev,
        questions: [...prev.questions, addedQuestion],
      }));
      setSaveMessage("Question added successfully");
    } catch (error) {
      console.error("Error adding question:", error);
      setSaveMessage("Error adding question");
    } finally {
      setIsSaving(false);
    }
  };

  // Update an existing question
  const handleUpdateQuestion = async (questionId: string, updatedQuestion: Partial<Question>) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/questions/${questionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedQuestion),
      });

      if (!response.ok) {
        throw new Error("Failed to update question");
      }

      const updatedQuestionData = await response.json();
      setQuiz((prev) => ({
        ...prev,
        questions: prev.questions.map((q) =>
          q.id === questionId ? { ...q, ...updatedQuestionData } : q
        ),
      }));
      setSaveMessage("Question updated successfully");
    } catch (error) {
      console.error("Error updating question:", error);
      setSaveMessage("Error updating question");
    } finally {
      setIsSaving(false);
    }
  };

  // Delete a question
  const handleDeleteQuestion = async (questionId: string) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/questions/${questionId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete question");
      }

      setQuiz((prev) => ({
        ...prev,
        questions: prev.questions.filter((q) => q.id !== questionId),
      }));
      setSaveMessage("Question deleted successfully");
    } catch (error) {
      console.error("Error deleting question:", error);
      setSaveMessage("Error deleting question");
    } finally {
      setIsSaving(false);
    }
  };

  // Publish or unpublish the quiz
  const handlePublishStatusChange = async (isPublished: boolean) => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      const response = await fetch(`/api/quizzes/${quiz.id}/publish`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isPublished }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${isPublished ? "publish" : "unpublish"} quiz`);
      }

      setQuiz((prev) => ({
        ...prev,
        isPublished,
      }));
      setSaveMessage(`Quiz ${isPublished ? "published" : "unpublished"} successfully`);
    } catch (error) {
      console.error(`Error ${isPublished ? "publishing" : "unpublishing"} quiz:`, error);
      setSaveMessage(`Error ${isPublished ? "publishing" : "unpublishing"} quiz`);
    } finally {
      setIsSaving(false);
    }
  };

  // Clear save message after 3 seconds
  useEffect(() => {
    if (saveMessage) {
      const timer = setTimeout(() => {
        setSaveMessage(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [saveMessage]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Edit Quiz</h1>
        <div className="flex items-center gap-4">
          {saveMessage && (
            <p className={`text-sm ${saveMessage.includes("Error") ? "text-red-500" : "text-green-500"}`}>
              {saveMessage}
            </p>
          )}
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard/quizzes")}
          >
            Back to Quizzes
          </Button>
          <Button
            onClick={() => router.push(`/dashboard/quizzes/${quiz.id}/preview`)}
          >
            Preview Quiz
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 w-full max-w-3xl">
          <TabsTrigger value="details">Quiz Details</TabsTrigger>
          <TabsTrigger value="questions">Questions</TabsTrigger>
          <TabsTrigger value="pools">Question Pools</TabsTrigger>
          <TabsTrigger value="publish">Publish</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Quiz Details</CardTitle>
              <CardDescription>
                Edit the basic information for your quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QuizDetailsForm
                quiz={quiz}
                onSave={handleSaveQuizDetails}
                isSaving={isSaving}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions">
          <Card>
            <CardHeader>
              <CardTitle>Questions</CardTitle>
              <CardDescription>
                Add, edit, or remove questions from your quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              <QuestionsManager
                questions={quiz.questions}
                onAddQuestion={handleAddQuestion}
                onUpdateQuestion={handleUpdateQuestion}
                onDeleteQuestion={handleDeleteQuestion}
                isSaving={isSaving}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pools">
          <Card>
            <CardHeader>
              <CardTitle>Question Pools</CardTitle>
              <CardDescription>
                Create pools of questions for dynamic selection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PoolsManager
                quiz={quiz}
                setQuiz={setQuiz}
                isSaving={isSaving}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="publish">
          <Card>
            <CardHeader>
              <CardTitle>Publish Settings</CardTitle>
              <CardDescription>
                Control the visibility and availability of your quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PublishSettings
                quiz={quiz}
                onPublishStatusChange={handlePublishStatusChange}
                isSaving={isSaving}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
