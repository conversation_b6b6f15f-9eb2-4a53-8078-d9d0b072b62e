"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { SystemSetting } from "@/lib/settings";

interface AdminSettingsClientProps {
  initialSettings: Record<string, SystemSetting[]>;
  category: string;
}

export default function AdminSettingsClient({ 
  initialSettings, 
  category 
}: AdminSettingsClientProps) {
  const [settings, setSettings] = useState(initialSettings[category] || []);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => 
      prev.map(setting => 
        setting.key === key 
          ? { ...setting, value }
          : setting
      )
    );
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: settings.map(s => ({ key: s.key, value: s.value }))
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      const result = await response.json();
      
      // Check if any settings failed to update
      const failures = result.results.filter((r: any) => !r.success);
      
      if (failures.length > 0) {
        toast({
          title: "Partial Success",
          description: `${result.results.length - failures.length} settings saved, ${failures.length} failed`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Settings Saved",
          description: "All settings have been updated successfully",
        });
        setHasChanges(false);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async (key: string) => {
    try {
      const response = await fetch(`/api/admin/settings/${encodeURIComponent(key)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to reset setting');
      }

      const result = await response.json();
      
      setSettings(prev => 
        prev.map(setting => 
          setting.key === key 
            ? { ...setting, value: result.value }
            : setting
        )
      );

      toast({
        title: "Setting Reset",
        description: `${key} has been reset to default value`,
      });
    } catch (error) {
      console.error('Error resetting setting:', error);
      toast({
        title: "Error",
        description: "Failed to reset setting. Please try again.",
        variant: "destructive",
      });
    }
  };

  const renderSettingInput = (setting: SystemSetting) => {
    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={setting.key}
              checked={setting.value}
              onCheckedChange={(checked) => handleSettingChange(setting.key, checked)}
            />
            <Label htmlFor={setting.key} className="text-sm">
              {setting.value ? 'Enabled' : 'Disabled'}
            </Label>
          </div>
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, parseInt(e.target.value) || 0)}
            className="w-32"
          />
        );
      
      case 'string':
        return (
          <Input
            type="text"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            className="w-64"
          />
        );
      
      default:
        return (
          <Input
            type="text"
            value={JSON.stringify(setting.value)}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleSettingChange(setting.key, parsed);
              } catch {
                // Invalid JSON, don't update
              }
            }}
            className="w-64 font-mono text-xs"
          />
        );
    }
  };

  const getFeatureIcon = (key: string) => {
    if (key.includes('questionPools')) return '🎲';
    if (key.includes('learningPaths')) return '🛤️';
    if (key.includes('analytics')) return '📊';
    if (key.includes('gamification')) return '🏆';
    if (key.includes('security')) return '🔒';
    if (key.includes('ui') || key.includes('theme')) return '🎨';
    if (key.includes('content')) return '📝';
    return '⚙️';
  };

  if (!settings.length) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No settings found for this category.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {settings.map((setting) => (
        <div key={setting.key} className="border rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-lg">{getFeatureIcon(setting.key)}</span>
                <h4 className="font-medium">{setting.title}</h4>
                {setting.key.includes('questionPools') && (
                  <Badge variant="secondary" className="text-xs">
                    Disabled by Default
                  </Badge>
                )}
                {!setting.isPublic && (
                  <Badge variant="outline" className="text-xs">
                    Admin Only
                  </Badge>
                )}
              </div>
              
              {setting.description && (
                <p className="text-sm text-muted-foreground mb-3">
                  {setting.description}
                </p>
              )}
              
              <div className="flex items-center gap-4">
                {renderSettingInput(setting)}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleReset(setting.key)}
                  className="text-xs"
                >
                  Reset
                </Button>
              </div>
              
              <div className="mt-2 text-xs text-muted-foreground">
                Key: <code className="bg-muted px-1 rounded">{setting.key}</code>
              </div>
            </div>
          </div>
        </div>
      ))}

      {hasChanges && (
        <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-blue-800">
              You have unsaved changes
            </span>
          </div>
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            size="sm"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      )}
    </div>
  );
}
