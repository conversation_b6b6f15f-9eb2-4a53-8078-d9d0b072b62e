import { PrismaClient } from '../src/generated/prisma';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

// Safe JSON parsing function with error handling
function safeJsonParse(value: any, context: string): any {
  if (typeof value !== 'string') {
    return value; // Already parsed or not a string
  }

  try {
    return JSON.parse(value);
  } catch (error) {
    console.error(`Failed to parse JSON for ${context}:`, error);
    console.error(`Value was:`, value);

    // Return a safe default based on context
    if (context.includes('options')) {
      return [];
    } else if (context.includes('correctAnswers')) {
      return [];
    } else if (context.includes('stems') || context.includes('correctPairs')) {
      return [];
    } else if (context.includes('blanks')) {
      return [];
    } else if (context.includes('hint')) {
      return [];
    } else {
      return null;
    }
  }
}

async function exportAllQuizzes() {
  try {
    console.log('🚀 Starting quiz export...');

    // Fetch all quizzes with related data
    const quizzes = await prisma.quiz.findMany({
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          },
        },
        questions: true,
        questionPools: {
          include: {
            questions: true,
          },
        },
        selectionRules: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    console.log(`📚 Found ${quizzes.length} quizzes to export`);

    // Convert quizzes to QFJSON format
    const exportData = {
      export_metadata: {
        export_date: new Date().toISOString(),
        total_quizzes: quizzes.length,
        total_questions: quizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0),
        format_version: "1.1",
        exported_by: "QuizFlow Export Script",
        description: "Complete export of all QuizFlow quizzes in QFJSON format"
      },
      quizzes: quizzes.map((quiz) => {
        // Convert database quiz to QFJSON format
        const qfjsonQuiz = {
          quiz: {
            $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
            metadata: {
              format_version: quiz.formatVersion,
              quiz_id: quiz.quizId,
              title: quiz.title,
              description: quiz.description || undefined,
              author: quiz.creator?.name || quiz.author || undefined,
              creation_date: quiz.creationDate.toISOString(),
              last_modified: quiz.updatedAt.toISOString(),
              tags: quiz.tags,
              category: quiz.category || undefined,
              difficulty: quiz.difficulty || undefined,
              passing_score_percentage: quiz.passingScore || undefined,
              time_limit_minutes: quiz.timeLimit || undefined,
              markup_format: quiz.markupFormat as "markdown" | "html" | "plain_text",
              locale: quiz.locale,
              // Include answer display configuration
              answer_display_config: {
                show_correct_answer: quiz.showCorrectAnswer,
                show_user_answer: quiz.showUserAnswer,
                show_explanation_after_answer: quiz.showExplanationAfterAnswer,
                highlight_correct_answer: quiz.highlightCorrectAnswer,
                immediate_answer_feedback: quiz.immediateAnswerFeedback,
              },
              // Include additional metadata
              is_published: quiz.isPublished,
              creator_email: quiz.creator?.email || undefined,
              source_type: quiz.sourceType,
              database_id: quiz.id,
            },
            questions: quiz.questions.map((q: any) => ({
              question_id: q.questionId,
              type: q.type,
              text: q.text,
              // Parse JSON fields back to objects with error handling
              options: q.options ? safeJsonParse(q.options, `question ${q.questionId} options`) : undefined,
              correct_answer: q.correctAnswer ? safeJsonParse(q.correctAnswer, `question ${q.questionId} correctAnswer`) : undefined,
              correct_answers: q.correctAnswers ? safeJsonParse(q.correctAnswers, `question ${q.questionId} correctAnswers`) : undefined,
              stems: q.stems ? safeJsonParse(q.stems, `question ${q.questionId} stems`) : undefined,
              correct_pairs: q.correctPairs ? safeJsonParse(q.correctPairs, `question ${q.questionId} correctPairs`) : undefined,
              text_template: q.textTemplate || undefined,
              blanks: q.blanks ? safeJsonParse(q.blanks, `question ${q.questionId} blanks`) : undefined,
              hint: q.hint ? safeJsonParse(q.hint, `question ${q.questionId} hint`) : undefined,
              explanation: q.explanation || undefined,
              feedback_correct: q.feedbackCorrect || undefined,
              feedback_incorrect: q.feedbackIncorrect || undefined,
              media: q.media || undefined,
              // Include additional metadata
              difficulty: q.difficulty || undefined,
              topic_tags: q.topicTags || undefined,
              skill_level: q.skillLevel || undefined,
              estimated_time: q.estimatedTime || undefined,
              real_world_scenario: q.realWorldScenario || undefined,
              cve_reference: q.cveReference || undefined,
              tools_required: q.toolsRequired || undefined,
              source_reference: q.sourceReference || undefined,
              case_sensitive: q.caseSensitive || undefined,
              single_correct_answer: q.singleCorrectAnswer || undefined,
            })),
            question_pools: quiz.questionPools.length > 0
              ? quiz.questionPools.map((pool: any) => ({
                  pool_id: pool.poolId,
                  title: pool.title || undefined,
                  description: pool.description || undefined,
                  questions: pool.questions.map((q: any) => ({
                    question_id: q.questionId,
                    type: q.type,
                    text: q.text,
                    // Parse JSON fields back to objects with error handling
                    options: q.options ? safeJsonParse(q.options, `pool question ${q.questionId} options`) : undefined,
                    correct_answer: q.correctAnswer ? safeJsonParse(q.correctAnswer, `pool question ${q.questionId} correctAnswer`) : undefined,
                    correct_answers: q.correctAnswers ? safeJsonParse(q.correctAnswers, `pool question ${q.questionId} correctAnswers`) : undefined,
                    stems: q.stems ? safeJsonParse(q.stems, `pool question ${q.questionId} stems`) : undefined,
                    correct_pairs: q.correctPairs ? safeJsonParse(q.correctPairs, `pool question ${q.questionId} correctPairs`) : undefined,
                    text_template: q.textTemplate || undefined,
                    blanks: q.blanks ? safeJsonParse(q.blanks, `pool question ${q.questionId} blanks`) : undefined,
                    hint: q.hint ? safeJsonParse(q.hint, `pool question ${q.questionId} hint`) : undefined,
                    explanation: q.explanation || undefined,
                    feedback_correct: q.feedbackCorrect || undefined,
                    feedback_incorrect: q.feedbackIncorrect || undefined,
                    media: q.media || undefined,
                    difficulty: q.difficulty || undefined,
                    topic_tags: q.topicTags || undefined,
                    skill_level: q.skillLevel || undefined,
                    estimated_time: q.estimatedTime || undefined,
                    real_world_scenario: q.realWorldScenario || undefined,
                    cve_reference: q.cveReference || undefined,
                    tools_required: q.toolsRequired || undefined,
                    source_reference: q.sourceReference || undefined,
                    case_sensitive: q.caseSensitive || undefined,
                    single_correct_answer: q.singleCorrectAnswer || undefined,
                  })),
                }))
              : undefined,
            selection_rules: quiz.selectionRules.length > 0
              ? quiz.selectionRules.map((rule: any) => ({
                  pool_id: rule.poolId,
                  select_count: rule.selectCount,
                  randomize: rule.randomize,
                  shuffle_order: rule.shuffleOrder,
                }))
              : undefined,
          },
        };

        return qfjsonQuiz;
      }),
    };

    // Create exports directory if it doesn't exist
    const exportsDir = path.join(process.cwd(), 'exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const filename = `quizflow-complete-export-${timestamp}.json`;
    const filepath = path.join(exportsDir, filename);

    // Write the export data to file
    fs.writeFileSync(filepath, JSON.stringify(exportData, null, 2), 'utf8');

    console.log('✅ Export completed successfully!');
    console.log(`📁 File saved to: ${filepath}`);
    console.log(`📊 Export statistics:`);
    console.log(`   📚 Total quizzes: ${exportData.export_metadata.total_quizzes}`);
    console.log(`   ❓ Total questions: ${exportData.export_metadata.total_questions}`);
    console.log(`   📦 File size: ${(fs.statSync(filepath).size / 1024 / 1024).toFixed(2)} MB`);

    return filepath;

  } catch (error) {
    console.error('❌ Error during export:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the export if this script is executed directly
if (require.main === module) {
  exportAllQuizzes()
    .then((filepath) => {
      console.log(`🎉 All quizzes exported successfully to: ${filepath}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Export failed:', error);
      process.exit(1);
    });
}

export default exportAllQuizzes;
