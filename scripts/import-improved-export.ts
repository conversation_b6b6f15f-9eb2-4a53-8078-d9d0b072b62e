#!/usr/bin/env tsx

/**
 * Import Improved QuizFlow Export
 * 
 * This script imports the improved quiz export file with quality fixes
 * and updates the database with the enhanced content.
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync } from 'fs';
import { join } from 'path';
import { getLocalizedText } from '@/lib/utils/qfjson-parser';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

interface ExportMetadata {
  export_date: string;
  total_quizzes: number;
  total_questions: number;
  format_version: string;
  exported_by: string;
  description: string;
}

interface QuizExport {
  export_metadata: ExportMetadata;
  quizzes: Array<{
    quiz: {
      $schema?: string;
      metadata: {
        format_version: string;
        quiz_id: string;
        title: string;
        description?: string;
        author?: string;
        creation_date: string;
        last_modified?: string;
        tags?: string[];
        passing_score_percentage?: number;
        time_limit_minutes?: number;
        markup_format?: string;
        locale?: string;
        answer_display_config?: any;
        is_published?: boolean;
        creator_email?: string;
        source_type?: string;
        database_id?: string;
      };
      questions?: any[];
    };
  }>;
}

async function ensureAdminUser() {
  console.log('👤 Ensuring admin user exists...');
  
  const adminEmail = '<EMAIL>';
  let adminUser = await prisma.user.findUnique({
    where: { email: adminEmail }
  });

  if (!adminUser) {
    console.log('🔧 Creating admin user...');
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    adminUser = await prisma.user.create({
      data: {
        email: adminEmail,
        name: 'QuizFlow Admin',
        password: hashedPassword,
        role: 'admin',
        emailVerified: new Date(),
      }
    });
    console.log('✅ Admin user created');
  } else {
    console.log('✅ Admin user already exists');
  }

  return adminUser;
}

async function clearExistingData() {
  console.log('🧹 Clearing existing quiz data...');
  
  // Delete in correct order due to foreign key constraints
  await prisma.question.deleteMany({});
  await prisma.quiz.deleteMany({});
  
  console.log('✅ Existing data cleared');
}

async function importQuizzes() {
  console.log('📥 Starting import of improved quiz export...');

  try {
    // Ensure admin user exists
    const adminUser = await ensureAdminUser();

    // Read the improved export file
    const exportPath = join(process.cwd(), 'exports/quizflow-complete-export-2025-05-26.json');
    console.log(`📖 Reading export file: ${exportPath}`);
    
    const fileContent = readFileSync(exportPath, 'utf-8');
    const exportData: QuizExport = JSON.parse(fileContent);

    console.log(`📊 Export metadata:`);
    console.log(`   - Export date: ${exportData.export_metadata.export_date}`);
    console.log(`   - Total quizzes: ${exportData.export_metadata.total_quizzes}`);
    console.log(`   - Total questions: ${exportData.export_metadata.total_questions}`);
    console.log(`   - Format version: ${exportData.export_metadata.format_version}`);

    // Ask for confirmation before clearing data
    console.log('\n⚠️  This will replace ALL existing quiz data!');
    console.log('Press Ctrl+C to cancel, or any key to continue...');
    
    // Clear existing data
    await clearExistingData();

    let importedQuizzes = 0;
    let importedQuestions = 0;
    let skippedQuizzes = 0;

    // Process each quiz
    for (const quizWrapper of exportData.quizzes) {
      const quiz = quizWrapper.quiz;
      const metadata = quiz.metadata;

      console.log(`\n📚 Processing quiz: ${metadata.title}`);

      try {
        // Check if quiz already exists (shouldn't after clearing, but just in case)
        const existingQuiz = await prisma.quiz.findUnique({
          where: { quizId: metadata.quiz_id }
        });

        if (existingQuiz) {
          console.log(`⚠️  Quiz ${metadata.quiz_id} already exists, skipping...`);
          skippedQuizzes++;
          continue;
        }

        // Create the quiz
        const createdQuiz = await prisma.quiz.create({
          data: {
            quizId: metadata.quiz_id,
            title: getLocalizedText(metadata.title, metadata.locale),
            description: getLocalizedText(metadata.description, metadata.locale) || '',
            author: metadata.author || 'QuizFlow Admin',
            tags: metadata.tags || [],
            passingScore: metadata.passing_score_percentage || 70,
            timeLimit: metadata.time_limit_minutes || 30,
            markupFormat: metadata.markup_format || 'markdown',
            locale: metadata.locale || 'en-US',
            formatVersion: metadata.format_version,
            isPublished: metadata.is_published !== false,
            creatorId: adminUser.id,
          }
        });

        console.log(`✅ Created quiz: ${createdQuiz.title}`);
        importedQuizzes++;

        // Create questions if they exist
        if (quiz.questions && quiz.questions.length > 0) {
          let questionCount = 0;
          
          for (const question of quiz.questions) {
            try {
              // Prepare question data
              const questionData: any = {
                questionId: question.question_id,
                type: question.type,
                text: typeof question.text === 'string' ? question.text : JSON.stringify(question.text),
                points: question.points || 1,
                feedbackCorrect: question.feedback_correct || null,
                feedbackIncorrect: question.feedback_incorrect || null,
                explanation: question.explanation ? JSON.stringify(question.explanation) : null,
                media: question.media ? JSON.stringify(question.media) : null,
                hint: question.hint ? JSON.stringify(question.hint) : null,
                topicTags: question.topic_tags || [],
                toolsRequired: question.tools_required || [],
                quizId: createdQuiz.id,
              };

              // Handle different question types
              if (question.options) {
                questionData.options = JSON.stringify(question.options);
              }
              if (question.correct_answer !== undefined) {
                questionData.correctAnswer = JSON.stringify(question.correct_answer);
              }
              if (question.correct_answers) {
                questionData.correctAnswers = JSON.stringify(question.correct_answers);
              }
              if (question.stems) {
                questionData.stems = JSON.stringify(question.stems);
              }
              if (question.correct_pairs) {
                questionData.correctPairs = JSON.stringify(question.correct_pairs);
              }
              if (question.text_template) {
                questionData.textTemplate = JSON.stringify(question.text_template);
              }
              if (question.blanks) {
                questionData.blanks = JSON.stringify(question.blanks);
              }

              await prisma.question.create({
                data: questionData
              });

              questionCount++;
              importedQuestions++;
            } catch (error) {
              console.error(`❌ Error creating question ${question.question_id}:`, error);
            }
          }

          console.log(`   📝 Created ${questionCount} questions`);
        }

      } catch (error) {
        console.error(`❌ Error processing quiz ${metadata.quiz_id}:`, error);
        skippedQuizzes++;
      }
    }

    console.log('\n🎉 Import completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Imported quizzes: ${importedQuizzes}`);
    console.log(`   - Imported questions: ${importedQuestions}`);
    console.log(`   - Skipped quizzes: ${skippedQuizzes}`);

  } catch (error) {
    console.error('❌ Import failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
if (require.main === module) {
  importQuizzes()
    .then(() => {
      console.log('✅ Import script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Import script failed:', error);
      process.exit(1);
    });
}

export { importQuizzes };
