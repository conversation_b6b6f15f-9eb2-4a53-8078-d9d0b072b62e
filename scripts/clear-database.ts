#!/usr/bin/env tsx

/**
 * Clear Database Script for QuizFlow
 *
 * This script removes all quizzes, questions, and related data from the database
 * while preserving user accounts and system data.
 */

import { PrismaClient } from '../src/generated/prisma';

const db = new PrismaClient();

async function clearDatabase() {
  console.log('🗑️  Starting database cleanup...');

  try {
    // Start a transaction to ensure data consistency
    await db.$transaction(async (tx) => {
      console.log('📊 Deleting user responses and quiz attempts...');
      await tx.userResponse.deleteMany({});

      console.log('🎓 Deleting learning path enrollments...');
      await tx.learningPathEnrollment.deleteMany({});

      console.log('📚 Deleting learning paths...');
      await tx.learningPath.deleteMany({});

      console.log('🎯 Deleting user skills...');
      await tx.userSkill.deleteMany({});

      console.log('📝 Deleting questions...');
      await tx.question.deleteMany({});

      console.log('🎯 Deleting question pools...');
      await tx.questionPool.deleteMany({});

      console.log('📋 Deleting selection rules...');
      await tx.selectionRule.deleteMany({});

      console.log('🧩 Deleting quizzes...');
      await tx.quiz.deleteMany({});

      console.log('📂 Deleting categories (handling hierarchy)...');
      // First delete child categories (those with parentId)
      await tx.category.deleteMany({
        where: {
          parentId: { not: null }
        }
      });
      // Then delete parent categories
      await tx.category.deleteMany({
        where: {
          parentId: null
        }
      });

      console.log('⚡ Deleting difficulty levels (optional)...');
      await tx.difficultyLevel.deleteMany({});
    });

    console.log('✅ Database cleared successfully!');
    console.log('\n📊 Summary:');
    console.log('   - All quizzes removed');
    console.log('   - All questions removed');
    console.log('   - All quiz attempts removed');
    console.log('   - All question pools removed');
    console.log('   - All selection rules removed');
    console.log('   - All categories removed');
    console.log('   - All difficulty levels removed');
    console.log('   - User accounts preserved');
    console.log('   - System settings preserved');

    // Verify the cleanup
    const remainingQuizzes = await db.quiz.count();
    const remainingQuestions = await db.question.count();
    const remainingAttempts = await db.userResponse.count();

    console.log('\n🔍 Verification:');
    console.log(`   - Quizzes remaining: ${remainingQuizzes}`);
    console.log(`   - Questions remaining: ${remainingQuestions}`);
    console.log(`   - Quiz attempts remaining: ${remainingAttempts}`);

    if (remainingQuizzes === 0 && remainingQuestions === 0 && remainingAttempts === 0) {
      console.log('\n🎉 Database successfully cleared!');
    } else {
      console.log('\n⚠️  Some data may still remain. Please check manually.');
    }

  } catch (error) {
    console.error('❌ Error clearing database:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  clearDatabase()
    .then(() => {
      console.log('\n✅ Database cleanup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database cleanup failed:', error);
      process.exit(1);
    });
}

export { clearDatabase };
