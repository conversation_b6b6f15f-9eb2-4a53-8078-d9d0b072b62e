import fs from 'fs';
import path from 'path';

interface ExportData {
  export_metadata: {
    export_date: string;
    total_quizzes: number;
    total_questions: number;
    format_version: string;
    exported_by: string;
    description: string;
  };
  quizzes: any[];
}

async function verifyExport(filename?: string) {
  try {
    console.log('🔍 Starting export verification...');

    // Find the most recent export file if no filename provided
    const exportsDir = path.join(process.cwd(), 'exports');
    let filepath: string;

    if (filename) {
      filepath = path.join(exportsDir, filename);
    } else {
      const files = fs.readdirSync(exportsDir)
        .filter(file => file.startsWith('quizflow-complete-export-') && file.endsWith('.json'))
        .sort()
        .reverse();
      
      if (files.length === 0) {
        throw new Error('No export files found in exports directory');
      }
      
      filepath = path.join(exportsDir, files[0]);
      console.log(`📁 Using most recent export: ${files[0]}`);
    }

    // Check if file exists
    if (!fs.existsSync(filepath)) {
      throw new Error(`Export file not found: ${filepath}`);
    }

    // Get file stats
    const stats = fs.statSync(filepath);
    const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`📦 File size: ${fileSizeMB} MB`);

    // Parse JSON
    console.log('📖 Parsing JSON...');
    const rawData = fs.readFileSync(filepath, 'utf8');
    const exportData: ExportData = JSON.parse(rawData);

    // Verify metadata
    console.log('✅ Verifying export metadata...');
    const metadata = exportData.export_metadata;
    console.log(`   📅 Export date: ${metadata.export_date}`);
    console.log(`   📚 Total quizzes: ${metadata.total_quizzes}`);
    console.log(`   ❓ Total questions: ${metadata.total_questions}`);
    console.log(`   🔢 Format version: ${metadata.format_version}`);
    console.log(`   🤖 Exported by: ${metadata.exported_by}`);

    // Verify quiz count matches
    const actualQuizCount = exportData.quizzes.length;
    if (actualQuizCount !== metadata.total_quizzes) {
      console.warn(`⚠️  Quiz count mismatch: metadata says ${metadata.total_quizzes}, found ${actualQuizCount}`);
    } else {
      console.log(`✅ Quiz count verified: ${actualQuizCount}`);
    }

    // Count actual questions
    let totalQuestions = 0;
    let quizzesWithPools = 0;
    let quizzesWithRules = 0;
    const categories = new Map<string, number>();
    const difficulties = new Map<string, number>();
    const questionTypes = new Map<string, number>();

    console.log('🔍 Analyzing quiz content...');
    
    for (const quizWrapper of exportData.quizzes) {
      const quiz = quizWrapper.quiz;
      
      // Count questions
      if (quiz.questions) {
        totalQuestions += quiz.questions.length;
        
        // Analyze question types
        for (const question of quiz.questions) {
          const type = question.type;
          questionTypes.set(type, (questionTypes.get(type) || 0) + 1);
        }
      }

      // Count pools and rules
      if (quiz.question_pools && quiz.question_pools.length > 0) {
        quizzesWithPools++;
      }
      if (quiz.selection_rules && quiz.selection_rules.length > 0) {
        quizzesWithRules++;
      }

      // Analyze categories and difficulties
      const category = quiz.metadata.category || 'Uncategorized';
      const difficulty = quiz.metadata.difficulty || 'Unknown';
      
      categories.set(category, (categories.get(category) || 0) + 1);
      difficulties.set(difficulty, (difficulties.get(difficulty) || 0) + 1);
    }

    // Verify question count
    if (totalQuestions !== metadata.total_questions) {
      console.warn(`⚠️  Question count mismatch: metadata says ${metadata.total_questions}, found ${totalQuestions}`);
    } else {
      console.log(`✅ Question count verified: ${totalQuestions}`);
    }

    // Display analysis results
    console.log('\n📊 Content Analysis:');
    
    console.log('\n📁 Categories:');
    for (const [category, count] of Array.from(categories.entries()).sort((a, b) => b[1] - a[1])) {
      console.log(`   ${category}: ${count} quizzes`);
    }

    console.log('\n🎯 Difficulties:');
    for (const [difficulty, count] of Array.from(difficulties.entries()).sort((a, b) => b[1] - a[1])) {
      console.log(`   ${difficulty}: ${count} quizzes`);
    }

    console.log('\n❓ Question Types:');
    for (const [type, count] of Array.from(questionTypes.entries()).sort((a, b) => b[1] - a[1])) {
      console.log(`   ${type}: ${count} questions`);
    }

    console.log('\n🔧 Advanced Features:');
    console.log(`   📚 Quizzes with question pools: ${quizzesWithPools}`);
    console.log(`   ⚙️  Quizzes with selection rules: ${quizzesWithRules}`);

    // Verify QFJSON compliance
    console.log('\n🔍 Verifying QFJSON compliance...');
    let compliantQuizzes = 0;
    let hasAnswerDisplayConfig = 0;

    for (const quizWrapper of exportData.quizzes) {
      const quiz = quizWrapper.quiz;
      
      // Check for required QFJSON fields
      if (quiz.$schema && quiz.metadata && quiz.questions) {
        compliantQuizzes++;
      }

      // Check for new answer display configuration
      if (quiz.metadata.answer_display_config) {
        hasAnswerDisplayConfig++;
      }
    }

    console.log(`   ✅ QFJSON compliant quizzes: ${compliantQuizzes}/${actualQuizCount}`);
    console.log(`   ⚙️  Quizzes with answer display config: ${hasAnswerDisplayConfig}/${actualQuizCount}`);

    // Sample quiz validation
    console.log('\n🔍 Sample quiz validation...');
    if (exportData.quizzes.length > 0) {
      const sampleQuiz = exportData.quizzes[0].quiz;
      console.log(`   📝 Sample quiz: "${sampleQuiz.metadata.title}"`);
      console.log(`   🆔 Quiz ID: ${sampleQuiz.metadata.quiz_id}`);
      console.log(`   ❓ Questions: ${sampleQuiz.questions?.length || 0}`);
      console.log(`   🏷️  Tags: ${sampleQuiz.metadata.tags?.join(', ') || 'None'}`);
      
      if (sampleQuiz.metadata.answer_display_config) {
        const config = sampleQuiz.metadata.answer_display_config;
        console.log(`   ⚙️  Answer display config:`);
        console.log(`      - Show correct answer: ${config.show_correct_answer}`);
        console.log(`      - Show user answer: ${config.show_user_answer}`);
        console.log(`      - Show explanation: ${config.show_explanation_after_answer}`);
        console.log(`      - Highlight correct: ${config.highlight_correct_answer}`);
        console.log(`      - Immediate feedback: ${config.immediate_answer_feedback}`);
      }
    }

    console.log('\n✅ Export verification completed successfully!');
    console.log(`📁 Verified file: ${path.basename(filepath)}`);
    console.log(`📊 Summary: ${actualQuizCount} quizzes, ${totalQuestions} questions, ${fileSizeMB} MB`);

    return {
      filepath,
      metadata,
      actualQuizCount,
      totalQuestions,
      fileSizeMB,
      categories: Object.fromEntries(categories),
      difficulties: Object.fromEntries(difficulties),
      questionTypes: Object.fromEntries(questionTypes),
      compliantQuizzes,
      hasAnswerDisplayConfig,
    };

  } catch (error) {
    console.error('❌ Export verification failed:', error);
    throw error;
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  const filename = process.argv[2]; // Optional filename argument
  
  verifyExport(filename)
    .then((results) => {
      console.log('\n🎉 Verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

export default verifyExport;
